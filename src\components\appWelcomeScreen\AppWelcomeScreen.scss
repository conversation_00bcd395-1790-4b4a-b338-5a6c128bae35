/* ===== APP WELCOME SCREEN - MODERNO Y ELEGANTE ===== */

.app-welcome-screen {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--primary-color) 0%,
    var(--primary-color-dark) 50%,
    #2c5aa0 100%);
  color: white;
  text-align: center;
  overflow: hidden;
  animation: fadeInBg 1.5s ease-out;
  padding: 0 2rem;

  /* Overlay sutil para profundidad */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%);
    pointer-events: none;
  }
}

/* Logo elegante y moderno */
.welcome-logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.animated-logo {
  width: 200px;
  height: 200px;
  border-radius: 40px;
  animation: logoEntrance 1.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.4))
          drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));

  /* Fondo circular sutil para el logo transparente */
  &::before {
    content: '';
    position: absolute;
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    background: radial-gradient(circle,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.08) 40%,
      rgba(255, 255, 255, 0.02) 70%,
      transparent 100%);
    border-radius: 52px;
    animation: logoGlow 3s ease-in-out infinite;
    z-index: -1;
  }

  /* Anillo decorativo exterior */
  &::after {
    content: '';
    position: absolute;
    top: -16px;
    left: -16px;
    right: -16px;
    bottom: -16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 56px;
    animation: ringPulse 2.5s ease-in-out infinite;
    z-index: -2;
  }
}

/* Tipografía moderna y elegante */
.welcome-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
  animation: titleSlideIn 1.5s 0.5s both cubic-bezier(0.34, 1.56, 0.64, 1);
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;

  span {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    position: relative;

    /* Brillo sutil */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, transparent 100%);
      border-radius: 8px;
      animation: shimmer 3s ease-in-out infinite;
    }
  }
}

.welcome-subtitle {
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 3rem 0;
  font-weight: 400;
  letter-spacing: 0.5px;
  animation: subtitleFadeIn 1.5s 1s both;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

/* Elementos decorativos modernos */
.welcome-bubble {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  pointer-events: none;
  z-index: 1;
  backdrop-filter: blur(1px);
}

.welcome-bubble-1 {
  width: 200px;
  height: 200px;
  left: -80px;
  top: -60px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  animation: modernFloat 8s infinite ease-in-out;
}

.welcome-bubble-2 {
  width: 150px;
  height: 150px;
  right: -60px;
  top: 80px;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 237, 78, 0.1) 100%);
  animation: modernFloat 10s 1.5s infinite ease-in-out reverse;
}

.welcome-bubble-3 {
  width: 120px;
  height: 120px;
  left: 40px;
  bottom: -40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.03) 100%);
  animation: modernFloat 7s 0.8s infinite ease-in-out;
}

.welcome-bubble-4 {
  width: 180px;
  height: 180px;
  right: 15vw;
  top: 65vh;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 237, 78, 0.05) 100%);
  animation: modernFloat 12s 2s infinite ease-in-out reverse;
}

.welcome-bubble-5 {
  width: 90px;
  height: 90px;
  left: 65vw;
  top: 15vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.02) 100%);
  animation: modernFloat 9s 1.2s infinite ease-in-out;
}

.welcome-bubble-6 {
  width: 130px;
  height: 130px;
  right: 8vw;
  bottom: 12vh;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.25) 0%, rgba(255, 237, 78, 0.08) 100%);
  animation: modernFloat 11s 0.5s infinite ease-in-out reverse;
}

/* ===== ANIMACIONES MODERNAS ===== */

@keyframes fadeInBg {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes logoEntrance {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
    filter: blur(10px);
  }
  60% {
    opacity: 1;
    transform: scale(1.1) rotate(2deg);
    filter: blur(0px);
  }
  80% {
    transform: scale(0.95) rotate(-1deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: blur(0px);
  }
}

@keyframes logoGlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes ringPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.08);
    opacity: 0.5;
  }
}

@keyframes titleSlideIn {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 0.3;
    transform: translateX(100%);
  }
}

@keyframes modernFloat {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(2deg) scale(1.02);
  }
  50% {
    transform: translateY(-40px) translateX(-5px) rotate(-1deg) scale(1.05);
  }
  75% {
    transform: translateY(-20px) translateX(-10px) rotate(1deg) scale(1.02);
  }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .app-welcome-screen {
    padding: 0 1.5rem;
  }

  .welcome-logo-wrapper {
    margin-bottom: 2rem;
  }

  .animated-logo {
    width: 160px;
    height: 160px;
    border-radius: 32px;

    &::before {
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border-radius: 42px;
    }

    &::after {
      top: -12px;
      left: -12px;
      right: -12px;
      bottom: -12px;
      border-radius: 44px;
    }
  }

  .welcome-title {
    font-size: 2.8rem;
    margin-bottom: 1.2rem;
  }

  .welcome-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .app-welcome-screen {
    padding: 0 1rem;
  }

  .welcome-logo-wrapper {
    margin-bottom: 1.5rem;
  }

  .animated-logo {
    width: 120px;
    height: 120px;
    border-radius: 24px;

    &::before {
      top: -8px;
      left: -8px;
      right: -8px;
      bottom: -8px;
      border-radius: 32px;
    }

    &::after {
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border-radius: 34px;
      border-width: 1.5px;
    }
  }

  .welcome-title {
    font-size: 2.2rem;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .welcome-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Burbujas más pequeñas en móvil */
  .welcome-bubble-1 { width: 120px; height: 120px; left: -40px; top: -40px; }
  .welcome-bubble-2 { width: 90px; height: 90px; right: -30px; top: 50px; }
  .welcome-bubble-3 { width: 70px; height: 70px; left: 20px; bottom: -20px; }
  .welcome-bubble-4 { width: 100px; height: 100px; right: 10vw; top: 60vh; }
  .welcome-bubble-5 { width: 60px; height: 60px; left: 60vw; top: 10vh; }
  .welcome-bubble-6 { width: 80px; height: 80px; right: 5vw; bottom: 8vh; }
}
