/* ===== CREATE ACCOUNT MODERNO Y RESPONSIVO ===== */

/* Sobrescribir estilos globales de ion-content específicamente para CreateAccount */
.create-account-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 20px;
  
  /* Elementos decorativos modernos */
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
}

/* Elementos decorativos adicionales */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  pointer-events: none;
  z-index: 0;
  
  &.decoration-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -5%;
    animation: float 6s ease-in-out infinite;
  }
  
  &.decoration-2 {
    width: 150px;
    height: 150px;
    bottom: 15%;
    right: -10%;
    animation: float 8s ease-in-out infinite reverse;
  }
  
  &.decoration-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    left: 85%;
    animation: float 7s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* Sobrescribir ion-content específicamente para el componente CreateAccount */
ion-content.create-account-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* Regla más específica para sobrescribir completamente los estilos globales */
ion-page ion-content.create-account-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* ===== CONTENEDOR PRINCIPAL ===== */
.create-account-container {
  width: 100%;
  max-width: 400px;
  padding: 100px 0 20px 0;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  gap: 25px;
}

/* ===== LOGO Y HEADER ===== */
.create-account-logo {
  text-align: center;
  flex-shrink: 0;
  z-index: 2;
  position: relative;

  img, .fallback-logo {
    width: 70px;
    height: 70px;
    object-fit: contain;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 15px;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
  }

  .fallback-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    
    ion-icon {
      font-size: 35px;
      color: #ffffff;
    }
  }

  h1 {
    font-size: 1.6rem;
    font-weight: 700;
    margin: 0;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  p {
    font-size: 0.9rem;
    margin: 8px 0 0 0;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    letter-spacing: 0.3px;
  }
}

/* ===== TARJETA DE CREATE ACCOUNT ===== */
.create-account-card {
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 30px 25px;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(20px);
  flex-shrink: 0;

  /* Borde superior decorativo */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #4a6fa5;
    z-index: 3;
  }

  .create-account-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d4373;
    text-align: center;
    margin: 0 0 25px 0;
    letter-spacing: 0.3px;
  }
}

/* ===== FORMULARIO ===== */
.create-account-form {
  .form-item {
    --background: rgba(248, 249, 250, 0.8);
    --border-color: rgba(74, 111, 165, 0.2);
    --border-radius: 12px;
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 52px;
    margin-bottom: 18px;
    border: 1px solid rgba(74, 111, 165, 0.15);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      --border-color: rgba(74, 111, 165, 0.3);
      border-color: rgba(74, 111, 165, 0.3);
      box-shadow: 0 4px 12px rgba(74, 111, 165, 0.1);
    }

    &:focus-within {
      --border-color: #4a6fa5;
      border-color: #4a6fa5;
      box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
    }

    ion-label {
      --color: #4a6fa5;
      font-weight: 600;
      font-size: 0.85rem;
      letter-spacing: 0.2px;
      transform: translateY(-2px);
    }

    ion-input {
      --color: #2d4373;
      --placeholder-color: rgba(74, 111, 165, 0.5);
      font-size: 0.95rem;
      --padding-top: 12px;
      --padding-bottom: 12px;
      letter-spacing: 0.3px;
      font-weight: 500;
    }

    ion-icon {
      font-size: 1.1rem;
      margin-right: 10px;
      color: #4a6fa5;
      transition: color 0.3s ease;
      align-self: center;
      display: flex;
      align-items: center;
    }
  }

  .error-message {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: -14px;
    margin-bottom: 14px;
    padding: 6px 16px;
    font-weight: 600;
    background: rgba(220, 53, 69, 0.08);
    border-radius: 8px;
    border-left: 3px solid #dc3545;
    letter-spacing: 0.2px;
  }

  .create-account-button {
    --background: #4a6fa5;
    --background-activated: #2d4373;
    --background-hover: #415f91;
    --color: #ffffff;
    --border-radius: 12px;
    --box-shadow: 0 4px 16px rgba(74, 111, 165, 0.3);
    --ripple-color: rgba(255, 255, 255, 0.3);
    height: 50px;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-top: 18px;
    width: 100%;
    transition: all 0.3s ease;

    &:hover {
      --box-shadow: 0 6px 20px rgba(74, 111, 165, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    ion-spinner {
      --color: #ffffff;
    }
  }
}

/* ===== FOOTER Y OPCIONES SOCIALES ===== */
.create-account-footer {
  text-align: center;
  flex-shrink: 0;
  z-index: 2;

  .login-text {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 18px;
    letter-spacing: 0.2px;

    span {
      color: #ffffff;
      text-decoration: none;
      font-weight: 600;
      border-bottom: 1px solid rgba(255, 255, 255, 0.5);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-bottom-color: #ffffff;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
      }
    }
  }

  ion-text {
    p {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.8);
      margin: 12px 0;
      letter-spacing: 0.2px;
    }
  }

  .social-login {
    display: flex;
    justify-content: center;
    gap: 14px;
    margin-top: 16px;

    .social-button {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      ion-icon {
        font-size: 1.3rem;
        color: #ffffff;
      }
    }
  }
}

/* ===== ANIMACIONES ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.create-account-container {
  animation: fadeIn 0.8s ease-out;
}

.create-account-card {
  animation: fadeIn 1s ease-out 0.2s both;
}

.create-account-footer {
  animation: fadeIn 1.2s ease-out 0.4s both;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Pantallas grandes (desktop) */
@media (min-height: 900px) and (min-width: 768px) {
  .create-account-container {
    gap: 35px;
    max-width: 450px;
    padding: 120px 0 40px 0;
  }

  .create-account-logo {
    img, .fallback-logo {
      width: 90px;
      height: 90px;
      padding: 15px;
    }

    .fallback-logo ion-icon {
      font-size: 45px;
    }

    h1 {
      font-size: 1.8rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .create-account-card {
    padding: 40px 35px;

    .create-account-title {
      font-size: 1.7rem;
      margin-bottom: 30px;
    }
  }

  .create-account-form {
    .form-item {
      --min-height: 56px;
      margin-bottom: 20px;

      ion-input {
        font-size: 1rem;
        --padding-top: 14px;
        --padding-bottom: 14px;
      }

      ion-label {
        font-size: 0.9rem;
      }
    }

    .create-account-button {
      height: 54px;
      font-size: 1rem;
      margin-top: 20px;
    }
  }

  .create-account-footer {
    .login-text {
      font-size: 0.9rem;
      margin-bottom: 20px;
    }

    .social-login {
      gap: 16px;
      margin-top: 20px;

      .social-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 1.4rem;
        }
      }
    }
  }
}

/* Tablets */
@media (max-width: 768px) and (min-width: 481px) {
  .create-account-page {
    padding: 0 20px;
  }

  .create-account-container {
    max-width: 380px;
    gap: 22px;
    padding: 80px 0 30px 0;
  }

  .create-account-logo {
    img, .fallback-logo {
      width: 65px;
      height: 65px;
      padding: 10px;
    }

    .fallback-logo ion-icon {
      font-size: 32px;
    }

    h1 {
      font-size: 1.5rem;
    }

    p {
      font-size: 0.85rem;
    }
  }

  .create-account-card {
    padding: 28px 22px;

    .create-account-title {
      font-size: 1.4rem;
      margin-bottom: 22px;
    }
  }
}

/* Móviles */
@media (max-width: 480px) {
  .create-account-page {
    padding: 0 20px;
  }

  .create-account-container {
    max-width: 100%;
    gap: 18px;
    min-height: 100vh;
    justify-content: flex-start;
    padding: 60px 0 30px 0;
  }

  .create-account-logo {
    margin-bottom: 0;

    img, .fallback-logo {
      width: 60px;
      height: 60px;
      padding: 10px;
      margin-bottom: 12px;
    }

    .fallback-logo ion-icon {
      font-size: 30px;
    }

    h1 {
      font-size: 1.4rem;
      margin-bottom: 5px;
    }

    p {
      font-size: 0.8rem;
    }
  }

  .create-account-card {
    padding: 22px 18px;
    border-radius: 16px;
    margin: 0 15px;
    width: 90%;

    .create-account-title {
      font-size: 1.3rem;
      margin-bottom: 18px;
    }
  }

  .create-account-form {
    .form-item {
      --min-height: 48px;
      margin-bottom: 16px;
      --padding-start: 12px;
      --padding-end: 12px;
      --border-radius: 10px;

      ion-input {
        font-size: 0.9rem;
        --padding-top: 10px;
        --padding-bottom: 10px;
      }

      ion-label {
        font-size: 0.8rem;
      }

      ion-icon {
        font-size: 1rem;
        margin-right: 8px;
      }
    }

    .error-message {
      font-size: 0.75rem;
      margin-top: -12px;
      margin-bottom: 12px;
      padding: 5px 12px;
    }

    .create-account-button {
      height: 46px;
      font-size: 0.9rem;
      margin-top: 16px;
      --border-radius: 10px;
    }
  }

  .create-account-footer {
    .login-text {
      font-size: 0.8rem;
      margin-bottom: 14px;
    }

    ion-text p {
      font-size: 0.75rem;
      margin: 10px 0;
    }

    .social-login {
      gap: 12px;
      margin-top: 14px;

      .social-button {
        width: 40px;
        height: 40px;

        ion-icon {
          font-size: 1.2rem;
        }
      }
    }
  }
}

/* Móviles muy pequeños */
@media (max-width: 360px) {
  .create-account-page {
    padding: 0 15px;
  }

  .create-account-container {
    gap: 15px;
    justify-content: flex-start;
    padding: 50px 0 25px 0;
  }

  .create-account-logo {
    img, .fallback-logo {
      width: 55px;
      height: 55px;
      padding: 8px;
      margin-bottom: 10px;
    }

    .fallback-logo ion-icon {
      font-size: 28px;
    }

    h1 {
      font-size: 1.3rem;
    }

    p {
      font-size: 0.75rem;
    }
  }

  .create-account-card {
    padding: 18px 15px;
    border-radius: 14px;
    margin: 0 12px;

    .create-account-title {
      font-size: 1.2rem;
      margin-bottom: 16px;
    }
  }

  .create-account-form {
    .form-item {
      --min-height: 44px;
      margin-bottom: 14px;
      --padding-start: 10px;
      --padding-end: 10px;

      ion-input {
        font-size: 0.85rem;
        --padding-top: 8px;
        --padding-bottom: 8px;
      }

      ion-label {
        font-size: 0.75rem;
      }

      ion-icon {
        font-size: 0.95rem;
        margin-right: 6px;
      }
    }

    .error-message {
      font-size: 0.7rem;
      margin-top: -10px;
      margin-bottom: 10px;
      padding: 4px 10px;
    }

    .create-account-button {
      height: 42px;
      font-size: 0.85rem;
      margin-top: 14px;
    }
  }

  .create-account-footer {
    .login-text {
      font-size: 0.75rem;
      margin-bottom: 12px;
    }

    ion-text p {
      font-size: 0.7rem;
      margin: 8px 0;
    }

    .social-login {
      gap: 10px;
      margin-top: 12px;

      .social-button {
        width: 36px;
        height: 36px;

        ion-icon {
          font-size: 1.1rem;
        }
      }
    }
  }
}
