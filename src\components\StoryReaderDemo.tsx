// DEMO: Cómo usar el StoryReader con tus datos exactos
// Este archivo muestra cómo integrar el nuevo sistema de traducción

import React from 'react';
import { IonPage, IonContent } from '@ionic/react';
import StoryReader from '../components/story-reader/StoryReader';
import { processedStory } from '../utils/storyDataProcessor';

const StoryReaderDemo: React.FC = () => {
  // El objeto 'story' que StoryReader espera recibir via location.state
  const demoStory = processedStory;

  console.log('📖 Historia procesada:', demoStory);
  console.log('🔤 Frases traducidas:', demoStory.translatedPhrases.length);

  return (
    <IonPage>
      <IonContent>
        {/* 
          Normalmente StoryReader recibe el story via useLocation().state
          Pero para el demo, podemos simular pasándolo como prop
        */}
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
          <h2>🧪 Demo del Nuevo Sistema de Traducción</h2>
          
          <div style={{ background: '#f5f5f5', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
            <h3>📊 Estadísticas de la Historia:</h3>
            <p><strong>Título:</strong> {demoStory.title}</p>
            <p><strong>Idioma:</strong> {demoStory.language}</p>
            <p><strong>Dificultad:</strong> {demoStory.difficulty}</p>
            <p><strong>Total de frases:</strong> {demoStory.translatedPhrases.length}</p>
            <p><strong>Longitud del contenido:</strong> {demoStory.content.length} caracteres</p>
          </div>

          <div style={{ background: '#e8f4fd', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
            <h3>✨ Características del Nuevo Sistema:</h3>
            <ul>
              <li>🎯 <strong>Frases Completas:</strong> Cada frase está completamente subrayada y es clickeable</li>
              <li>🧹 <strong>Traducciones Limpias:</strong> Solo letras y espacios, sin puntuación innecesaria</li>
              <li>🔗 <strong>Emparejamiento Exacto:</strong> Cada frase inglesa tiene su traducción española</li>
              <li>🎨 <strong>Feedback Visual:</strong> Hover effects y tooltips informativos</li>
              <li>📱 <strong>Responsive:</strong> Funciona perfectamente en móvil y desktop</li>
            </ul>
          </div>

          <div style={{ background: '#fff3cd', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
            <h3>🔍 Ejemplo de Traducción:</h3>
            <p><strong>Original:</strong> "{demoStory.translatedPhrases[0].original}"</p>
            <p><strong>Traducción:</strong> "{demoStory.translatedPhrases[0].translated}"</p>
            <p><em>→ Al hacer click en la frase, verás la traducción limpia sin puntuación</em></p>
          </div>

          <div style={{ background: '#d1ecf1', padding: '15px', borderRadius: '8px' }}>
            <h3>🚀 Para usar con tus datos:</h3>
            <pre style={{ background: '#fff', padding: '10px', borderRadius: '4px', overflow: 'auto' }}>
{`// 1. Importar el procesador
import { processStoryData } from './utils/storyDataProcessor';

// 2. Tus datos tal como se renderizan actualmente
const myStoryData = {
  "English": [["My sentence."], ["Another sentence."]],
  "Spanish": [["Mi oración."], ["Otra oración."]]
};

// 3. Procesar los datos
const myStory = processStoryData(
  myStoryData, 
  "Mi Título", 
  "Beginner"
);

// 4. Usar en StoryReader
// El componente ya está listo para usar myStory`}
            </pre>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default StoryReaderDemo;
