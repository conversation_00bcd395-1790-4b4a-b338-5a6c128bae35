// DEMO: StoryReader con sistema de traducción interactivo
import React from 'react';
import { IonPage, IonContent, IonButton, IonText } from '@ionic/react';
import { IStorytale } from '../common/models/storytale.interface';

// Crear datos de prueba que simulan tu estructura real
const createDemoStory = (): IStorytale => {
  return {
    id: 149,
    userId: 'demo-user',
    title: '<PERSON>ri el erizo',
    content: '', // Vacío como en tu caso real
    language: 'English',
    difficulty: 'Medium',
    genre: { id: 2, name: 'Cómic<PERSON>', createdAt: '2025-05-20T05:28:57.000Z', updatedAt: '2025-05-20T05:28:57.000Z' },
    isDefault: false,
    createdAt: '2025-08-12T04:42:30.061Z',
    updatedAt: '2025-08-12T04:42:30.061Z',
    translatedPhrases: [
      { original: '<PERSON><PERSON> the hedgehog', translated: '<PERSON>ri el erizo' },
      { original: 'loved to explore', translated: 'le encantaba explorar' },
      { original: 'the forest near', translated: 'el bosque cerca' },
      { original: 'his cozy burrow', translated: 'de su madriguera acogedora' },
      { original: 'One sunny morning', translated: 'una mañana soleada' },
      { original: 'he found a', translated: 'encontró una' },
      { original: 'mysterious glowing stone', translated: 'piedra misteriosa que brillaba' },
      { original: 'hidden beneath', translated: 'escondida debajo' },
      { original: 'the old oak tree', translated: 'del viejo roble' },
      { original: 'The stone pulsed', translated: 'La piedra pulsaba' },
      { original: 'with magical energy', translated: 'con energía mágica' },
      { original: 'and whispered secrets', translated: 'y susurraba secretos' },
      { original: 'of ancient times', translated: 'de tiempos antiguos' },
      { original: 'Eri touched the stone', translated: 'Eri tocó la piedra' },
      { original: 'and suddenly felt', translated: 'y de repente sintió' },
      { original: 'a warm tingling', translated: 'un cosquilleo cálido' },
      { original: 'throughout his spines', translated: 'por todas sus púas' },
      { original: 'The forest around him', translated: 'El bosque a su alrededor' },
      { original: 'began to shimmer', translated: 'comenzó a brillar' },
      { original: 'and dance with colors', translated: 'y danzar con colores' },
      { original: 'he had never seen', translated: 'que nunca había visto' },
      { original: 'From that day forward', translated: 'Desde ese día en adelante' },
      { original: 'Eri could understand', translated: 'Eri pudo entender' },
      { original: 'the language of trees', translated: 'el lenguaje de los árboles' },
      { original: 'and speak with', translated: 'y hablar con' },
      { original: 'all forest creatures', translated: 'todas las criaturas del bosque' },
      { original: 'He became the', translated: 'Se convirtió en el' },
      { original: 'guardian of the woods', translated: 'guardián del bosque' },
      { original: 'protecting its magic', translated: 'protegiendo su magia' },
      { original: 'and keeping its', translated: 'y manteniendo sus' },
      { original: 'ancient secrets safe', translated: 'antiguos secretos a salvo' },
      { original: 'The other animals', translated: 'Los otros animales' },
      { original: 'came to him', translated: 'venían a él' },
      { original: 'for wisdom and', translated: 'por sabiduría y' },
      { original: 'guidance in times', translated: 'guía en tiempos' },
      { original: 'of trouble and need', translated: 'de problemas y necesidad' },
      { original: 'And Eri lived', translated: 'Y Eri vivió' },
      { original: 'happily ever after', translated: 'feliz para siempre' }
    ]
  };
};

const StoryReaderDemo: React.FC = () => {
  const demoStory = createDemoStory();

  const handleOpenStoryReader = () => {
    // Simular navegación al StoryReader con el story
    window.history.pushState({ story: demoStory }, '', '/story-reader');
    window.location.reload();
  };

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <IonText color="primary">
            <h1>📖 Demo StoryReader</h1>
          </IonText>

          <div style={{
            background: 'var(--background-secondary)',
            padding: '20px',
            borderRadius: '12px',
            margin: '20px 0',
            border: '2px solid var(--primary-color)'
          }}>
            <h2>✨ Funcionalidades Implementadas</h2>
            <ul style={{ textAlign: 'left', maxWidth: '600px', margin: '0 auto' }}>
              <li>🎯 <strong>Frases clickeables:</strong> Cada frase muestra su traducción al hacer click</li>
              <li>🔊 <strong>Text-to-Speech:</strong> Reproduce todo el cuento en voz alta</li>
              <li>🎨 <strong>Tooltips modernos:</strong> Traducciones en ventanas elegantes</li>
              <li>🌙 <strong>Tema dual:</strong> Soporte para modo claro y oscuro</li>
              <li>📱 <strong>Responsive:</strong> Funciona en móvil y desktop</li>
              <li>⏯️ <strong>Controles de audio:</strong> Play, pause y reset</li>
            </ul>
          </div>

          <div style={{
            background: 'var(--primary-color)',
            color: 'white',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0'
          }}>
            <h3>📊 Historia de Prueba</h3>
            <p><strong>Título:</strong> {demoStory.title}</p>
            <p><strong>Frases:</strong> {demoStory.translatedPhrases.length}</p>
            <p><strong>Idioma:</strong> {demoStory.language}</p>
            <p><strong>Dificultad:</strong> {demoStory.difficulty}</p>
          </div>

          <IonButton
            expand="block"
            size="large"
            onClick={handleOpenStoryReader}
            style={{ margin: '20px 0' }}
          >
            🚀 Abrir StoryReader
          </IonButton>

          <div style={{
            background: 'var(--background-tertiary)',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '0.9rem',
            color: 'var(--text-medium)'
          }}>
            <p><strong>Instrucciones:</strong></p>
            <p>1. Haz click en "Abrir StoryReader"</p>
            <p>2. Haz click en cualquier frase para ver su traducción</p>
            <p>3. Usa el botón de play para escuchar el cuento</p>
            <p>4. Cambia entre tema claro/oscuro con el botón del header</p>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default StoryReaderDemo;
