import {
  IonButton,
  IonContent,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonPage,
  IonSpinner,
  IonText,
} from "@ionic/react";
import { useForm } from "react-hook-form";
import { useAuth } from "../../hooks/useAuth";
import { useState } from "react";
import {
  personOutline,
  mailOutline,
  lockClosedOutline,
  logoGoogle,
  logoApple,
  logoFacebook,
  eyeOutline,
  eyeOffOutline,
  bookOutline,
} from "ionicons/icons";
import "./CreateAccount.scss";
import logoBlanco from "../../assets/logo-blanco.png";
import { useHistory } from "react-router";
import { useBusy } from "../../hooks/useBusy";
import useToast from "../../hooks/useToast";

interface CreateAccountFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const CreateAccount: React.FC = () => {
  const { register } = useAuth();
  const { showError } = useToast();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [logoError, setLogoError] = useState(false);
  const history = useHistory();

  const {
    register: registerField,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<CreateAccountFormData>();

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleRegister = () => {
    handleSubmit(
      async (data: CreateAccountFormData) => {
        setLoading(true);
        try {
          await register(data.name, data.email, data.password);
          // El registro automáticamente hace login y el router manejará la navegación
          // Si el email no está verificado, el login redirigirá a la pantalla de verificación

        } catch (error: any) {
          showError(
            "Error al crear la cuenta: " +
              (error.message || "Por favor intenta nuevamente")
          );
        } finally {
          setLoading(false);
        }
      },
      () => {
        showError("Por favor, completa todos los campos correctamente");
      }
    )();
  };

  const goToLoginPage = () => {
    history.push("/login");
  };

  return (
    <IonPage>
      <IonContent className="create-account-page">
        {/* Elementos decorativos modernos */}
        <div className="bg-decoration decoration-1"></div>
        <div className="bg-decoration decoration-2"></div>
        <div className="bg-decoration decoration-3"></div>

        <div className="create-account-container">
          <div className="create-account-logo">
            {logoError ? (
              <div className="fallback-logo">
                <IonIcon icon={bookOutline} />
              </div>
            ) : (
              <img
                src={logoBlanco}
                alt="Story App Logo"
                onError={() => setLogoError(true)}
              />
            )}
            <h1>Story App</h1>
            <p>Únete y aprende idiomas con historias</p>
          </div>

          <div className="create-account-card">
            <h2 className="create-account-title">Crear Cuenta</h2>

            <div className="create-account-form">
              {/* Campo Nombre */}
              <IonItem className="form-item">
                <IonIcon icon={personOutline} slot="start" color="primary" />
                <IonLabel position="floating">Nombre</IonLabel>
                <IonInput
                  type="text"
                  {...registerField("name", {
                    required: true,
                    minLength: 2,
                  })}
                />
              </IonItem>
              {errors.name?.type === "required" && (
                <div className="error-message">El nombre es requerido</div>
              )}
              {errors.name?.type === "minLength" && (
                <div className="error-message">
                  El nombre debe tener al menos 2 caracteres
                </div>
              )}

              {/* Campo Email */}
              <IonItem className="form-item">
                <IonIcon icon={mailOutline} slot="start" color="primary" />
                <IonLabel position="floating">Email</IonLabel>
                <IonInput
                  type="email"
                  {...registerField("email", {
                    required: true,
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  })}
                />
              </IonItem>
              {errors.email?.type === "required" && (
                <div className="error-message">El email es requerido</div>
              )}
              {errors.email?.type === "pattern" && (
                <div className="error-message">Ingresa un email válido</div>
              )}

              {/* Campo Contraseña */}
              <IonItem className="form-item">
                <IonIcon
                  icon={lockClosedOutline}
                  slot="start"
                  color="primary"
                />
                <IonLabel position="floating">Contraseña</IonLabel>
                <IonInput
                  type={showPassword ? "text" : "password"}
                  {...registerField("password", {
                    required: true,
                    minLength: 6,
                  })}
                />
                <IonIcon
                  slot="end"
                  icon={showPassword ? eyeOffOutline : eyeOutline}
                  onClick={togglePasswordVisibility}
                  style={{ cursor: "pointer", color: "#4a6fa5" }}
                />
              </IonItem>
              {errors.password?.type === "required" && (
                <div className="error-message">La contraseña es requerida</div>
              )}
              {errors.password?.type === "minLength" && (
                <div className="error-message">
                  La contraseña debe tener al menos 6 caracteres
                </div>
              )}

              {/* Campo Confirmar Contraseña */}
              <IonItem className="form-item">
                <IonIcon
                  icon={lockClosedOutline}
                  slot="start"
                  color="primary"
                />
                <IonLabel position="floating">Confirmar Contraseña</IonLabel>
                <IonInput
                  type={showConfirmPassword ? "text" : "password"}
                  {...registerField("confirmPassword", {
                    required: true,
                    validate: (value) => {
                      const passwordValue = getValues("password");
                      return value === passwordValue || "Las contraseñas no coinciden";
                    },
                  })}
                />
                <IonIcon
                  slot="end"
                  icon={showConfirmPassword ? eyeOffOutline : eyeOutline}
                  onClick={toggleConfirmPasswordVisibility}
                  style={{ cursor: "pointer", color: "#4a6fa5" }}
                />
              </IonItem>
              {errors.confirmPassword?.type === "required" && (
                <div className="error-message">Confirma tu contraseña</div>
              )}
              {errors.confirmPassword?.message && (
                <div className="error-message">{errors.confirmPassword.message}</div>
              )}

              <IonButton
                className="create-account-button"
                expand="block"
                disabled={loading}
                onClick={handleRegister}
              >
                {loading ? (
                  <>
                    <IonSpinner name="crescent" />
                    <span style={{ marginLeft: "10px" }}>Creando cuenta...</span>
                  </>
                ) : (
                  "Crear Cuenta"
                )}
              </IonButton>
            </div>
          </div>

          <div className="create-account-footer">
            <div className="login-text">
              ¿Ya tienes una cuenta? <span onClick={goToLoginPage}>Inicia Sesión</span>
            </div>

            <IonText color="medium">
              <p>O regístrate con</p>
            </IonText>

            <div className="social-login">
              <div className="social-button">
                <IonIcon icon={logoGoogle} />
              </div>
              <div className="social-button">
                <IonIcon icon={logoFacebook} />
              </div>
              <div className="social-button">
                <IonIcon icon={logoApple} />
              </div>
            </div>
          </div>
        </div>


      </IonContent>
    </IonPage>
  );
};

export default CreateAccount;
