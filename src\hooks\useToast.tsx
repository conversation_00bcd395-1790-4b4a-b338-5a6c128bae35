// Re-export del contexto para mantener compatibilidad
export { useToast, ToastProvider } from "../contexts/ToastContext";
export type { ToastColor, ToastPosition } from "../contexts/ToastContext";

// Hook legacy para compatibilidad (deprecated)
import { useToast as useToastContext } from "../contexts/ToastContext";

const useToast = () => {
  const context = useToastContext();

  // Retorna el contexto con un ToastComponent vacío para compatibilidad
  return {
    ...context,
    ToastComponent: () => null, // El toast ya se renderiza en el provider
  };
};

export default useToast;
