/* ===== STORY READER - DISEÑO AMIGABLE Y ESPACIADO ===== */

.container-story-reader {
  width: 100%;
  min-height: 100vh;
  background: var(--background-color);
  position: relative;
  padding: 0;
  margin: 0;
}

/* ===== FRASES CLICKEABLES FLUIDAS (ESTILO CUENTO) ===== */
.story-sentence {
  display: inline-block; /* Cambio a inline-block para controlar mejor el subrayado */
  margin: 0 4px 0 0; /* Margen derecho para separar frases */
  padding: 1px 2px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  position: relative;
  text-decoration: underline;
  text-decoration-color: var(--primary-color);
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
  text-decoration-style: dashed; /* Líneas discontinuas */

  /* Efecto hover sutil */
  &:hover {
    background: rgba(74, 111, 165, 0.1);
    text-decoration-thickness: 3px;
    text-decoration-style: dashed;
    transform: none;
    box-shadow: none;
  }

  &:active {
    background: rgba(74, 111, 165, 0.15);
    text-decoration-color: var(--primary-color-dark);
  }

  /* Remover el ::after ya que usamos margin para el espaciado */
}

/* Contenedor de texto fluido */
.text-story {
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 2.6;
  color: var(--text-dark);
  text-align: left;
  letter-spacing: 1.2px;
  word-spacing: 3px;
  position: relative;
  font-family: "Georgia", "Times New Roman", serif;
}

/* ===== TOOLTIP MODERNO DE TRADUCCIÓN ===== */
.modern-tooltip {
  width: 320px; /* Ancho fijo para cálculos precisos */
  max-width: calc(100vw - 40px); /* Responsive en pantallas pequeñas */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: tooltipSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  .translation-content {
    padding: 0;

    .translation-header {
      background: linear-gradient(
        135deg,
        var(--primary-color) 0%,
        var(--primary-color-dark) 100%
      );
      color: white;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 12px;

      .original-sentence {
        font-weight: 600;
        font-size: 0.95rem;
        line-height: 1.4;
        flex: 1;
        opacity: 0.95;
      }

      .header-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .play-phrase-btn,
      .close-translation-btn {
        --color: rgba(255, 255, 255, 0.9);
        --background: rgba(255, 255, 255, 0.1);
        --border-radius: 8px;
        --padding-start: 8px;
        --padding-end: 8px;
        --padding-top: 8px;
        --padding-bottom: 8px;
        margin: 0;
        min-width: 32px;
        min-height: 32px;
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;

        &:hover {
          --background: rgba(255, 255, 255, 0.2);
          transform: scale(1.05);
        }

        ion-icon {
          font-size: 16px;
        }
      }

      .play-phrase-btn {
        --color: rgba(255, 255, 255, 1);

        &:hover {
          --background: rgba(255, 255, 255, 0.25);
        }

        ion-icon {
          font-size: 18px;
          color: rgba(255, 255, 255, 1);
        }
      }
    }

    .translation-text {
      padding: 20px;
      font-size: 1.1rem;
      line-height: 1.6;
      color: var(--text-dark);
      font-weight: 500;
      background: rgba(255, 255, 255, 0.9);
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 20px;
        right: 20px;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(74, 111, 165, 0.2) 50%,
          transparent 100%
        );
      }
    }
  }
}

@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== DARK THEME PARA FRASES FLUIDAS ===== */
body.dark-theme {
  .story-sentence {
    color: var(--text-light);
    text-decoration-color: var(--primary-color-light);
    text-decoration-style: dashed; /* Mantener líneas discontinuas en dark theme */

    &:hover {
      background: rgba(74, 111, 165, 0.2);
      text-decoration-color: var(--primary-color);
      text-decoration-style: dashed;
    }

    &:active {
      background: rgba(74, 111, 165, 0.25);
    }
  }

  .text-story {
    color: var(--text-light);
  }

  .modern-tooltip {
    background: rgba(32, 32, 32, 0.95);
    box-shadow:
      0 20px 50px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1);

    .translation-content {
      .translation-text {
        background: rgba(32, 32, 32, 0.9);
        color: var(--text-light);
      }
    }
  }

  .translation-overlay {
    background: rgba(0, 0, 0, 0.3); /* Fondo más oscuro en dark theme */
  }
}

/* ===== RESPONSIVE PARA FRASES FLUIDAS ===== */
@media (max-width: 768px) {
  .story-sentence {
    margin: 0 3px 0 0; /* Margen reducido en móvil */
    padding: 1px 2px;
    text-decoration-thickness: 1.5px;
    text-underline-offset: 2px;
    text-decoration-style: dashed;

    &:hover {
      text-decoration-thickness: 2px;
      text-decoration-style: dashed;
    }
  }

  .text-story {
    font-size: 1.25rem;
    line-height: 2.3;
    letter-spacing: 0.9px;
    word-spacing: 2px;
  }

  .modern-tooltip {
    min-width: 250px;
    max-width: calc(100vw - 40px);

    .translation-content {
      .translation-header {
        padding: 14px 16px;

        .original-sentence {
          font-size: 0.9rem;
        }

        .close-translation-btn {
          min-width: 28px;
          min-height: 28px;
        }
      }

      .translation-text {
        padding: 16px;
        font-size: 1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .story-sentence {
    margin: 0 2px 0 0; /* Margen aún más reducido en móvil pequeño */
    padding: 1px 2px;
    text-decoration-thickness: 1px;
    text-decoration-style: dashed;

    &:hover {
      text-decoration-thickness: 1.5px;
      text-decoration-style: dashed;
    }
  }

  .text-story {
    font-size: 1.15rem;
    line-height: 2.2;
    letter-spacing: 0.7px;
    word-spacing: 1.5px;
  }

  .modern-tooltip {
    min-width: 220px;

    .translation-content {
      .translation-header {
        padding: 12px 14px;

        .original-sentence {
          font-size: 0.85rem;
        }
      }

      .translation-text {
        padding: 14px;
        font-size: 0.95rem;
      }
    }
  }
}

/* ===== PALABRAS CLICKEABLES Y TRADUCIBLES (LEGACY) ===== */
.clickable-word {
  transition: all 0.2s ease;
  border-radius: 3px;
  padding: 1px 2px;

  &.translatable {
    cursor: pointer;

    &:hover {
      background-color: var(--ion-color-primary-tint);
      color: var(--ion-color-primary-contrast);
    }

    &.predefined {
      text-decoration: underline;
      text-decoration-color: var(--ion-color-primary);
      text-decoration-thickness: 2px;
      text-underline-offset: 2px;

      &:hover {
        text-decoration-color: var(--ion-color-primary-shade);
        background-color: var(--ion-color-primary);
        color: white;
      }
    }

    /* Estilo especial para frases completas */
    &.complete-phrase {
      background: rgba(74, 111, 165, 0.08);
      border-radius: 6px;
      padding: 2px 4px;
      font-weight: 500;

      &:hover {
        background: rgba(74, 111, 165, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);
      }

      &:active {
        transform: translateY(0);
        background: rgba(74, 111, 165, 0.2);
      }
    }
  }
}

/* ===== TOOLTIP DE TRADUCCIÓN MEJORADO ===== */
.translation-tooltip {
  min-width: 250px;
  max-width: 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--ion-color-light);
  overflow: hidden;
  animation: tooltipFadeIn 0.3s ease;

  .translation-content {
    padding: 0;

    .translation-header {
      background: var(--ion-color-primary);
      color: white;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .original-word {
        font-weight: 600;
        font-size: 1rem;
      }

      .translation-source {
        .source-badge {
          font-size: 0.75rem;
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;

          &.predefined {
            background: var(--ion-color-success);
            color: white;
          }

          &.cache {
            background: var(--ion-color-warning);
            color: white;
          }

          &.api {
            background: var(--ion-color-secondary);
            color: white;
          }
        }
      }

      .close-translation-btn {
        --color: white;
        --background: transparent;
        --padding-start: 4px;
        --padding-end: 4px;
        margin: 0;
        min-width: 24px;
        min-height: 24px;
      }
    }

    .translation-text {
      padding: 16px;
      font-size: 1rem;
      line-height: 1.5;
      color: var(--ion-text-color);

      .loading-container {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--ion-color-medium);

        ion-spinner {
          width: 16px;
          height: 16px;
        }
      }

      .error-container {
        .error-text {
          color: var(--ion-color-danger);
          font-size: 0.9rem;
          display: block;
          margin-bottom: 4px;
        }

        .fallback-text {
          color: var(--ion-color-medium);
          font-size: 0.9rem;
        }
      }
    }
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.translation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.1); /* Fondo semi-transparente para indicar que es clickeable */
  backdrop-filter: blur(2px); /* Efecto de desenfoque sutil */
}

/* Header minimalista - solo botón de tema con acento de color */
.header-story-reader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--background-color);
  z-index: 1000;
  padding: 16px 20px;
  border-bottom: 2px solid var(--primary-color);
  box-shadow: 0 2px 8px var(--shadow-color-light);

  .header-spacer {
    width: 40px;
  }

  .theme-toggle-btn {
    --background: var(--primary-color);
    --color: white;
    --border-radius: 50%;
    --padding-start: 0;
    --padding-end: 0;
    width: 40px;
    height: 40px;
    margin: 0;
    box-shadow: 0 2px 8px rgba(74, 111, 165, 0.3);

    ion-icon {
      font-size: 1.3rem;
      color: white;
      transition: all 0.3s ease;
    }

    &:hover {
      --background: var(--primary-color-dark);
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(74, 111, 165, 0.4);
    }
  }
}

/* Contenedor principal - ELEGANTE Y MODERNO */
.text-container {
  padding: 100px 64px 160px; /* Mucho más padding para elegancia */
  max-width: 900px;
  margin: 0 auto;
  background: var(--background-color);
  position: relative;
  border-radius: 24px; /* Bordes redondeados elegantes */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08); /* Sombra sutil */

  /* Esquinas decorativas refinadas */
  &::after {
    content: "";
    position: absolute;
    top: 16px;
    left: 16px;
    width: 60px;
    height: 60px;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      transparent 70%
    );
    border-radius: 50%;
    opacity: 0.1;
  }
}

/* Título del cuento - ELEGANTE Y MODERNO */
.title-story-reader {
  font-size: 2.8rem; /* Título más grande */
  font-weight: 700;
  text-align: center;
  color: var(--primary-color);
  margin: 0 0 32px 0;
  line-height: 1.2;
  letter-spacing: -0.5px; /* Espaciado negativo para elegancia */
  text-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);
  font-family: "Georgia", "Times New Roman", serif;
  position: relative;

  /* Línea decorativa debajo del título */
  &::after {
    content: "";
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--primary-color) 20%,
      var(--primary-color-light) 50%,
      var(--primary-color) 80%,
      transparent 100%
    );
    border-radius: 2px;
  }
}

/* Subtítulo - con acento de color principal */
.subtitle-story-reader {
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  color: var(--primary-color);
  margin: 0 0 48px 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  opacity: 0.8;
  position: relative;

  /* Línea decorativa con color principal */
  &::after {
    content: "";
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      var(--primary-color),
      transparent
    );
  }
}

/* Asegurar visibilidad en dark theme */
body.dark-theme .subtitle-story-reader {
  color: var(--primary-color-light);
  opacity: 1;
}

/* Footer en dark theme */
body.dark-theme .footer-story-reader {
  background: var(--background-tertiary); /* Color sólido para dark theme */
}

/* Texto del cuento - ELEGANTE Y MODERNO SIN LÍNEAS LATERALES */
.text-story {
  font-size: 1.4rem; /* LETRA MÁS GRANDE */
  font-weight: 400;
  line-height: 2.6; /* LÍNEAS SÚPER ESPACIADAS */
  color: var(--text-dark);
  text-align: left;
  letter-spacing: 1.2px; /* MÁS ESPACIADO ENTRE LETRAS */
  word-spacing: 3px; /* MÁS ESPACIADO ENTRE PALABRAS */
  position: relative;
  font-family: "Georgia", "Times New Roman", serif; /* Fuente más elegante */

  .story-paragraph {
    margin: 0 0 24px 0; /* ESPACIO REDUCIDO entre párrafos */
    text-indent: 0;
    letter-spacing: 1.2px;
    word-spacing: 3px;
    position: relative;

    /* Primera letra elegante y destacada */
    &:first-of-type::first-letter {
      font-size: 2.8rem; /* PRIMERA LETRA MÁS GRANDE */
      font-weight: 700;
      color: var(--primary-color);
      float: left;
      line-height: 0.8;
      margin: 8px 6px 0 0; /* Espaciado perfecto */
      font-family: "Georgia", serif;
      text-shadow: 0 2px 4px rgba(74, 111, 165, 0.2);
      position: relative;

      /* Fondo sutil para la primera letra */
      &::before {
        content: "";
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        background: radial-gradient(
          circle,
          rgba(74, 111, 165, 0.1) 0%,
          transparent 70%
        );
        border-radius: 8px;
        z-index: -1;
      }
    }

    /* Resto de párrafos sin primera letra especial */
    &:not(:first-of-type)::first-letter {
      font-size: inherit;
      font-weight: inherit;
      color: inherit;
      float: none;
      margin: 0;
      letter-spacing: inherit;
    }
  }
}

/* Contador para párrafos */
.text-story {
  counter-reset: paragraph;
}

/* Footer moderno - ELEMENTOS COMPLETAMENTE SEPARADOS CON COLOR SÓLIDO */
.footer-story-reader {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr; /* Tres columnas iguales */
  align-items: center;
  padding: 32px 48px;
  background: var(--background-secondary); /* COLOR SÓLIDO */
  border-top: 3px solid var(--primary-color);
  height: 120px; /* Footer más alto para elegancia */
  z-index: 1000;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

/* Botón de reiniciar - COMPLETAMENTE SEPARADO */
.reset-audio {
  justify-self: start; /* Alineado a la izquierda */
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.8rem;
  color: var(--primary-color);
  cursor: pointer;
  border-radius: 20px; /* Bordes menos redondeados para modernidad */
  background: var(--background-color);
  border: 3px solid var(--primary-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(74, 111, 165, 0.15);

  &:hover {
    color: white;
    background: var(--primary-color);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 24px rgba(74, 111, 165, 0.3);
  }
}

/* Botón de play/pause principal - CENTRADO Y ELEGANTE */
.play {
  justify-self: center; /* Centrado en el grid */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.icon-play {
  font-size: 5rem; /* Botón aún más grande */
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 6px 16px rgba(74, 111, 165, 0.3));
  position: relative;

  &:hover {
    color: var(--primary-color-dark);
    transform: scale(1.15);
    filter: drop-shadow(0 12px 24px rgba(74, 111, 165, 0.4));
  }

  /* Círculo de fondo elegante */
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(
      circle,
      rgba(74, 111, 165, 0.1) 0%,
      rgba(74, 111, 165, 0.05) 50%,
      transparent 70%
    );
    border-radius: 50%;
    z-index: -1;
    transition: all 0.4s ease;
  }

  &:hover::before {
    width: 120px;
    height: 120px;
    background: radial-gradient(
      circle,
      rgba(74, 111, 165, 0.2) 0%,
      rgba(74, 111, 165, 0.1) 50%,
      transparent 70%
    );
  }
}

/* Botón de configuración - COMPLETAMENTE SEPARADO */
.config {
  justify-self: end; /* Alineado a la derecha */
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.8rem;
  color: var(--primary-color);
  cursor: pointer;
  border-radius: 20px; /* Bordes menos redondeados para modernidad */
  background: var(--background-color);
  border: 3px solid var(--primary-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(74, 111, 165, 0.15);

  &:hover {
    color: white;
    background: var(--primary-color);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 24px rgba(74, 111, 165, 0.3);
  }
}

/* Menú de configuración */
ion-menu {
  --background: var(--background-color);
  --width: 320px;

  ion-content {
    --background: var(--background-color);
  }

  ion-item {
    --background: transparent;
    --color: var(--text-dark);
    --border-color: var(--border-color-light);
  }
}

/* Indicador de reproducción - amigable */
.playing-indicator {
  position: absolute;
  top: 24px;
  right: 24px;
  color: var(--text-dark);
  background: var(--primary-color);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 2px 8px var(--shadow-color);

  ion-icon {
    font-size: 1.1rem;
    color: white;
  }
}

/* ===== RESPONSIVE - DISEÑO AMIGABLE ===== */

/* Tablets */
@media (max-width: 1024px) {
  .text-container {
    padding: 80px 48px 140px;
    max-width: 100%;
    border-radius: 20px;
  }

  .title-story-reader {
    font-size: 2.4rem;
  }

  .text-story {
    font-size: 1.3rem; /* Letra grande mantenida */
    line-height: 2.4; /* Líneas muy espaciadas */
    letter-spacing: 1px;
    word-spacing: 2.5px;

    .story-paragraph {
      margin-bottom: 20px; /* Espacio reducido en tablets */

      &:first-of-type::first-letter {
        font-size: 2.6rem; /* Primera letra grande */
        margin: 6px 5px 0 0;
      }
    }
  }
}

/* Mobile */
@media (max-width: 768px) {
  .header-story-reader {
    padding: 35px 16px 12px;

    .theme-toggle-btn {
      width: 36px;
      height: 36px;

      ion-icon {
        font-size: 1.2rem;
      }
    }
  }

  .text-container {
    padding: 100px 32px 140px; /* Más padding superior en móvil */
    border-radius: 16px;
  }

  .title-story-reader {
    font-size: 2.2rem;
    margin-bottom: 24px;
  }

  .subtitle-story-reader {
    font-size: 0.9rem;
    margin-bottom: 40px;
    letter-spacing: 1.2px;
  }

  .text-story {
    font-size: 1.25rem; /* Letra grande en móvil */
    line-height: 2.3; /* Líneas muy espaciadas */
    letter-spacing: 0.9px;
    word-spacing: 2px;

    .story-paragraph {
      margin-bottom: 18px; /* Espacio reducido en móvil */

      &:first-of-type::first-letter {
        font-size: 2.4rem; /* Primera letra grande en móvil */
        margin: 4px 4px 0 0;
      }
    }
  }

  .footer-story-reader {
    padding: 24px 32px;
    height: 100px;
    grid-template-columns: 1fr 1fr 1fr; /* Mantener grid */
  }

  .icon-play {
    font-size: 4rem; /* Botón grande en móvil */
  }

  .reset-audio,
  .config {
    width: 56px;
    height: 56px;
    font-size: 1.6rem;
    border-radius: 16px;
  }
}

/* Mobile pequeño */
@media (max-width: 480px) {
  .text-container {
    padding: 100px 24px 120px; /* Más padding superior en móvil pequeño */
    border-radius: 12px;
  }

  .title-story-reader {
    font-size: 1.9rem;
    margin-bottom: 20px;
  }

  .subtitle-story-reader {
    font-size: 0.85rem;
    margin-bottom: 36px;
  }

  .text-story {
    font-size: 1.15rem; /* Letra grande incluso en móvil pequeño */
    line-height: 2.2; /* Líneas bien espaciadas */
    letter-spacing: 0.7px;
    word-spacing: 1.5px;

    .story-paragraph {
      margin-bottom: 16px; /* Espacio reducido en móvil pequeño */

      &:first-of-type::first-letter {
        font-size: 2.2rem; /* Primera letra grande */
        margin: 3px 3px 0 0;
      }
    }
  }

  .footer-story-reader {
    padding: 20px 24px;
    height: 90px;
    grid-template-columns: 1fr 1fr 1fr; /* Mantener grid */
  }

  .icon-play {
    font-size: 3.5rem;
  }

  .reset-audio,
  .config {
    width: 52px;
    height: 52px;
    font-size: 1.5rem;
    border-radius: 14px;
  }

  /* Tooltip más pequeño en móvil */
  .translation-tooltip .translation-content {
    padding: 12px 16px;
    max-width: 280px;

    .translation-header {
      font-size: 0.9rem;
      margin-bottom: 8px;
    }

    .translation-text {
      font-size: 1rem;
    }
  }
}

/* ===== SISTEMA DE TRADUCCIÓN INTERACTIVA ===== */

/* Palabras clickeables */
.clickable-word {
  display: inline;
  transition: all 0.2s ease;
  border-radius: 3px;
  padding: 1px 2px;
  -webkit-tap-highlight-color: transparent; /* Remover highlight azul en móvil */

  &.translatable {
    color: var(--primary-color);
    background: rgba(74, 111, 165, 0.08);
    cursor: pointer;

    /* Palabras con traducción predefinida más destacadas */
    &.predefined {
      background: rgba(74, 111, 165, 0.12);
      font-weight: 500;
    }

    /* Hover solo en desktop */
    @media (hover: hover) {
      &:hover {
        background: rgba(74, 111, 165, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(74, 111, 165, 0.2);
      }

      &.predefined:hover {
        background: rgba(74, 111, 165, 0.2);
      }
    }

    /* Estados activos para móvil y desktop */
    &:active {
      transform: translateY(0);
      background: rgba(74, 111, 165, 0.2);
    }

    /* Mejor área de toque en móvil */
    @media (max-width: 768px) {
      padding: 2px 4px;
      margin: 1px;
      min-height: 24px;
      display: inline-flex;
      align-items: center;
    }
  }
}

/* Tooltip de traducción */
.translation-tooltip {
  position: fixed;
  z-index: 10000;
  animation: fadeInTooltip 0.2s ease-out;

  .translation-content {
    padding: 16px 20px;
    background: var(--background-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color-light);
    max-width: 320px;
    min-width: 200px;

    .translation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color-light);

      .original-word {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 1rem;
        font-family: "Georgia", "Times New Roman", serif;
        flex: 1;
      }

      .translation-source {
        margin: 0 8px;

        .source-badge {
          font-size: 0.7rem;
          padding: 2px 6px;
          border-radius: 8px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.predefined {
            background: rgba(40, 167, 69, 0.15);
            color: #28a745;
          }

          &.cache {
            background: rgba(255, 193, 7, 0.15);
            color: #ffc107;
          }

          &.api {
            background: rgba(23, 162, 184, 0.15);
            color: #17a2b8;
          }
        }
      }

      .close-translation-btn {
        --color: var(--text-medium);
        --padding-start: 4px;
        --padding-end: 4px;
        --padding-top: 4px;
        --padding-bottom: 4px;
        height: 28px;
        width: 28px;

        ion-icon {
          font-size: 16px;
        }
      }
    }

    .translation-text {
      color: var(--text-dark);
      font-size: 1.1rem;
      line-height: 1.4;
      font-weight: 500;
      font-family: "Georgia", "Times New Roman", serif;

      .loading-container {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--primary-color);
        font-size: 0.95rem;

        ion-spinner {
          --color: var(--primary-color);
          width: 16px;
          height: 16px;
        }
      }

      .error-container {
        .error-text {
          color: var(--danger-color);
          font-size: 0.9rem;
          display: block;
          margin-bottom: 4px;
        }

        .fallback-text {
          color: var(--text-medium);
          font-size: 0.85rem;
          font-style: italic;
        }
      }
    }
  }
}

/* Animación para el tooltip */
@keyframes fadeInTooltip {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dark theme para el sistema de traducción */
body.dark-theme {
  .clickable-word.translatable {
    background: rgba(74, 111, 165, 0.15);

    &.predefined {
      background: rgba(74, 111, 165, 0.2);
    }

    @media (hover: hover) {
      &:hover {
        background: rgba(74, 111, 165, 0.25);
      }

      &.predefined:hover {
        background: rgba(74, 111, 165, 0.3);
      }
    }

    &:active {
      background: rgba(74, 111, 165, 0.3);
    }

    /* Estilo especial para frases completas en dark theme */
    &.complete-phrase {
      background: rgba(74, 111, 165, 0.2);

      &:hover {
        background: rgba(74, 111, 165, 0.3);
        box-shadow: 0 2px 8px rgba(74, 111, 165, 0.4);
      }

      &:active {
        background: rgba(74, 111, 165, 0.4);
      }
    }
  }

  .translation-tooltip .translation-content {
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);

    .translation-source .source-badge {
      &.predefined {
        background: rgba(40, 167, 69, 0.25);
        color: #4caf50;
      }

      &.cache {
        background: rgba(255, 193, 7, 0.25);
        color: #ffeb3b;
      }

      &.api {
        background: rgba(23, 162, 184, 0.25);
        color: #29b6f6;
      }
    }
  }
}
