/* For information on how to create your own theme, please see:
http://ionicframework.com/docs/theming/ */

/* ===== TEMA LIGHT (por defecto) ===== */
:root {
  /* Colores principales */
  --primary-color: #4a6fa5;
  --primary-color-light: #6b8bc3;
  --primary-color-dark: #2d4373;
  --primary-color-tint: #e0f0ff;

  /* Colores de fondo */
  --background-color: #ffffff;
  --background-secondary: #f8f9fa;
  --background-tertiary: #f1f3f5;
  --surface-color: #ffffff;

  /* Colores de texto */
  --text-dark: #1a1a1a;
  --text-medium: #6c757d;
  --text-light: #adb5bd;
  --text-on-primary: #ffffff;

  /* Colores de borde y sombra */
  --border-color: #e9ecef;
  --border-color-light: #f8f9fa;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-light: rgba(0, 0, 0, 0.05);

  /* Colores de estado */
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;

  /* Colores específicos de la app */
  --card-background: #ffffff;
  --header-background: #ffffff;
  --tab-bar-background: #ffffff;
  --input-background: #ffffff;

  /* Ionic color overrides */
  --ion-color-primary: #4a6fa5;
  --ion-color-primary-rgb: 74, 111, 165;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #415f91;
  --ion-color-primary-tint: #5c7fb0;

  --ion-color-secondary: #6c757d;
  --ion-color-secondary-rgb: 108, 117, 125;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #5f676e;
  --ion-color-secondary-tint: #7b848a;

  --ion-color-success: #28a745;
  --ion-color-success-rgb: 40, 167, 69;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #24923d;
  --ion-color-success-tint: #3eb158;

  --ion-color-warning: #ffc107;
  --ion-color-warning-rgb: 255, 193, 7;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0a800;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #dc3545;
  --ion-color-danger-rgb: 220, 53, 69;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #c22e3c;
  --ion-color-danger-tint: #e04858;

  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;
  --ion-text-color: #1a1a1a;
  --ion-text-color-rgb: 26, 26, 26;
  --ion-border-color: #e9ecef;
  --ion-item-background: #ffffff;
}

/* ===== TEMA DARK ===== */
body.dark-theme {
  /* Colores principales */
  --primary-color: #6b8bc3;
  --primary-color-light: #8da5d1;
  --primary-color-dark: #4a6fa5;
  --primary-color-tint: rgba(107, 139, 195, 0.2);

  /* Colores de fondo */
  --background-color: #121212;
  --background-secondary: #1e1e1e;
  --background-tertiary: #2a2a2a;
  --surface-color: #1e1e1e;

  /* Colores de texto */
  --text-dark: #ffffff;
  --text-medium: #b3b3b3;
  --text-light: #808080;
  --text-on-primary: #ffffff;

  /* Colores de borde y sombra */
  --border-color: #333333;
  --border-color-light: #404040;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-light: rgba(0, 0, 0, 0.15);

  /* Colores de estado */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --info-color: #2196f3;

  /* Colores específicos de la app */
  --card-background: #1e1e1e;
  --header-background: #1e1e1e;
  --tab-bar-background: #1e1e1e;
  --input-background: #2a2a2a;

  /* Ionic color overrides para dark theme */
  --ion-color-primary: #6b8bc3;
  --ion-color-primary-rgb: 107, 139, 195;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5e7aab;
  --ion-color-primary-tint: #7a96c9;

  --ion-color-secondary: #b3b3b3;
  --ion-color-secondary-rgb: 179, 179, 179;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0, 0, 0;
  --ion-color-secondary-shade: #9e9e9e;
  --ion-color-secondary-tint: #bbbbbb;

  --ion-color-success: #4caf50;
  --ion-color-success-rgb: 76, 175, 80;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #439a46;
  --ion-color-success-tint: #5eb862;

  --ion-color-warning: #ff9800;
  --ion-color-warning-rgb: 255, 152, 0;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e08600;
  --ion-color-warning-tint: #ffa21a;

  --ion-color-danger: #f44336;
  --ion-color-danger-rgb: 244, 67, 54;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #d73c30;
  --ion-color-danger-tint: #f5564a;

  --ion-background-color: #121212;
  --ion-background-color-rgb: 18, 18, 18;
  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;
  --ion-border-color: #333333;
  --ion-item-background: #1e1e1e;
}
