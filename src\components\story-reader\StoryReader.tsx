import {
  IonContent,
  IonIcon,
  IonMenuButton,
  IonText,
  IonButton,
} from "@ionic/react";
import { TextToSpeech } from "@capacitor-community/text-to-speech";
import {
  caretForwardCircleOutline,
  settings,
  refresh,
  pause,
  moon,
  sunny,
} from "ionicons/icons";
import { Capacitor } from "@capacitor/core";

import "./StoryReader.scss";
import { useState } from "react";
import ConfigStoryReader from "../config-story-reader/ConfigStoryReader";
import { useLocation, useHistory } from "react-router";
import { IStorytale } from "../../common/models/storytale.interface";
import { useTheme } from "../../hooks/useTheme";

export default function StoryReader() {
  const [isPlaying, setIsPlaying] = useState(false);
  
  const location = useLocation();
  const history = useHistory();
  const { story } = location.state as { story: IStorytale };
  const { theme, toggleTheme } = useTheme();

  if (!story) {
    // Si no hay cuento, redirigimos de nuevo a la galería
    history.push("/");
    return null;
  }

  // Función para obtener el código de idioma para TextToSpeech
  const getLanguageCode = (language: string): string => {
    // Mapeo de idiomas a códigos de idioma para TextToSpeech
    const languageMap: { [key: string]: string } = {
      english: "en-US",
      spanish: "es-ES",
      french: "fr-FR",
      german: "de-DE",
      italian: "it-IT",
      portuguese: "pt-PT",
      russian: "ru-RU",
      japanese: "ja-JP",
      chinese: "zh-CN",
      // Añade más idiomas según sea necesario
    };

    // Convertir a minúsculas y buscar en el mapa
    const langKey = language.toLowerCase();
    return languageMap[langKey] || "en-US"; // Valor predeterminado: inglés
  };

  const readParagraph = async (text: string): Promise<void> => {
    await TextToSpeech.speak({
      text: text,
      lang: getLanguageCode(story.language),
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
      category: "ambient",
      queueStrategy: 1,
    });
  };

  const resetStory = (): void => {
    stopStory();
  };

  const stopStory = (): void => {
    TextToSpeech.stop();
    setIsPlaying(false);
  };

  return (
    <>
      <ConfigStoryReader />
      <IonContent className="container-story-reader" id="main-content">
        <header className="header-story-reader">
          <div className="header-spacer"></div>
          <IonButton
            fill="clear"
            onClick={toggleTheme}
            className="theme-toggle-btn"
            title="Cambiar tema"
          >
            <IonIcon icon={theme === "dark" ? sunny : moon} slot="icon-only" />
          </IonButton>
        </header>
        <div className="text-container">
          {isPlaying && (
            <div className="playing-indicator">
              <IonIcon icon={caretForwardCircleOutline} />
              Playing...
            </div>
          )}
          <IonText color="primary">
            <h1 className="title-story-reader">{story.title}</h1>
          </IonText>
          <h3 className="subtitle-story-reader">
            {story.difficulty} - {story.language}
          </h3>
          <div className="text-story">
          </div>
        </div>
        <footer className="footer-story-reader">
          <div className="reset-audio">
            <IonIcon onClick={resetStory} icon={refresh}></IonIcon>
          </div>
          <div className="play">
            {!isPlaying && (
              <IonIcon
                className="icon-play"
                icon={caretForwardCircleOutline}
              ></IonIcon>
            )}
            {isPlaying && (
              <IonIcon
                className="icon-play"
                onClick={stopStory}
                icon={pause}
              ></IonIcon>
            )}
          </div>
          <IonMenuButton autoHide={false} className="config">
            <IonIcon icon={settings}></IonIcon>
          </IonMenuButton>
        </footer>
      </IonContent>
    </>
  );
}
