import {
  IonContent,
  IonIcon,
  IonMenuButton,
  IonText,
  IonButton,
  IonSpinner,
} from "@ionic/react";
import { TextToSpeech } from "@capacitor-community/text-to-speech";
import {
  caretForwardCircleOutline,
  settings,
  refresh,
  pause,
  moon,
  sunny,
  closeOutline,
} from "ionicons/icons";
import { Capacitor } from "@capacitor/core";

import "./StoryReader.scss";
import { useState, useEffect, useRef } from "react";
import ConfigStoryReader from "../config-story-reader/ConfigStoryReader";
import { useLocation, useHistory } from "react-router";
import { IStorytale } from "../../common/models/storytale.interface";
import { useTheme } from "../../hooks/useTheme";

interface TranslationTooltip {
  phrase: string;
  translation: string;
  position: { x: number; y: number };
}

export default function StoryReader() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [tooltip, setTooltip] = useState<TranslationTooltip | null>(null);
  const [isLoadingTranslation, setIsLoadingTranslation] = useState(false);

  const location = useLocation();
  const history = useHistory();
  const { story } = location.state as { story: IStorytale };
  const { theme, toggleTheme } = useTheme();
  const overlayRef = useRef<HTMLDivElement>(null);

  if (!story) {
    // Si no hay cuento, redirigimos de nuevo a la galería
    history.push("/");
    return null;
  }

  // Construir el contenido del cuento desde translatedPhrases
  const storyContent = story.translatedPhrases.map(phrase => phrase.original).join(' ');

  if(story) {
    console.log("story: ", story);
    
  }

  // Función para obtener el código de idioma para TextToSpeech
  const getLanguageCode = (language: string): string => {
    const languageMap: { [key: string]: string } = {
      english: "en-US",
      spanish: "es-ES",
      french: "fr-FR",
      german: "de-DE",
      italian: "it-IT",
      portuguese: "pt-PT",
      russian: "ru-RU",
      japanese: "ja-JP",
      chinese: "zh-CN",
    };
    const langKey = language.toLowerCase();
    return languageMap[langKey] || "en-US";
  };

  // Función para manejar click en frases
  const handlePhraseClick = (phrase: string, event: React.MouseEvent) => {
    const translation = story.translatedPhrases.find(tp => tp.original === phrase);
    if (!translation) return;

    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    };

    setTooltip({
      phrase: phrase,
      translation: translation.translated,
      position
    });
  };

  // Función para cerrar tooltip
  const closeTooltip = () => {
    setTooltip(null);
  };

  // Función para leer todo el cuento
  const readFullStory = async (): Promise<void> => {
    if (isPlaying) {
      stopStory();
      return;
    }

    setIsPlaying(true);
    try {
      await TextToSpeech.speak({
        text: storyContent,
        lang: getLanguageCode(story.language),
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        category: "ambient",
        queueStrategy: 1,
      });
    } catch (error) {
      console.error('Error al reproducir TTS:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  const resetStory = (): void => {
    stopStory();
    setCurrentSentenceIndex(0);
  };

  const stopStory = (): void => {
    TextToSpeech.stop();
    setIsPlaying(false);
  };

  // Función para renderizar el contenido con frases clickeables
  const renderClickableContent = () => {
    const phrasesPerParagraph = 4; // Agrupar cada 4 frases en un párrafo
    const paragraphs = [];

    for (let i = 0; i < story.translatedPhrases.length; i += phrasesPerParagraph) {
      const paragraphPhrases = story.translatedPhrases.slice(i, i + phrasesPerParagraph);

      paragraphs.push(
        <div key={i} className="story-paragraph">
          {paragraphPhrases.map((phraseObj, index) => (
            <span
              key={i + index}
              className="story-sentence"
              onClick={(e) => handlePhraseClick(phraseObj.original, e)}
            >
              {phraseObj.original}
              {index < paragraphPhrases.length - 1 ? ' ' : ''}
            </span>
          ))}
        </div>
      );
    }

    return paragraphs;
  };

  // Efecto para cerrar tooltip al hacer click fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltip && overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
        closeTooltip();
      }
    };

    if (tooltip) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [tooltip]);

  return (
    <>
      <ConfigStoryReader />
      <IonContent className="container-story-reader" id="main-content">
        <header className="header-story-reader">
          <div className="header-spacer"></div>
          <IonButton
            fill="clear"
            onClick={toggleTheme}
            className="theme-toggle-btn"
            title="Cambiar tema"
          >
            <IonIcon icon={theme === "dark" ? sunny : moon} slot="icon-only" />
          </IonButton>
        </header>
        <div className="text-container">
          {isPlaying && (
            <div className="playing-indicator">
              <IonIcon icon={caretForwardCircleOutline} />
              Reproduciendo...
            </div>
          )}
          <IonText color="primary">
            <h1 className="title-story-reader">{story.title}</h1>
          </IonText>
          <h3 className="subtitle-story-reader">
            {story.difficulty} - {story.language}
          </h3>
          <div className="text-story">
            {renderClickableContent()}
          </div>
        </div>
        <footer className="footer-story-reader">
          <div className="reset-audio">
            <IonIcon onClick={resetStory} icon={refresh}></IonIcon>
          </div>
          <div className="play">
            {!isPlaying && (
              <IonIcon
                className="icon-play"
                onClick={readFullStory}
                icon={caretForwardCircleOutline}
              ></IonIcon>
            )}
            {isPlaying && (
              <IonIcon
                className="icon-play"
                onClick={stopStory}
                icon={pause}
              ></IonIcon>
            )}
          </div>
          <IonMenuButton autoHide={false} className="config">
            <IonIcon icon={settings}></IonIcon>
          </IonMenuButton>
        </footer>

        {/* Tooltip de traducción */}
        {tooltip && (
          <div className="translation-overlay" ref={overlayRef}>
            <div
              className="modern-tooltip"
              style={{
                position: 'fixed',
                left: `${Math.min(tooltip.position.x - 150, window.innerWidth - 320)}px`,
                top: `${Math.max(tooltip.position.y - 100, 20)}px`,
                zIndex: 10000
              }}
            >
              <div className="translation-content">
                <div className="translation-header">
                  <div className="original-sentence">{tooltip.phrase}</div>
                  <IonButton
                    fill="clear"
                    onClick={closeTooltip}
                    className="close-translation-btn"
                  >
                    <IonIcon icon={closeOutline} />
                  </IonButton>
                </div>
                <div className="translation-text">
                  {isLoadingTranslation ? (
                    <div className="loading-container">
                      <IonSpinner name="crescent" />
                      <span>Traduciendo...</span>
                    </div>
                  ) : (
                    tooltip.translation
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </IonContent>
    </>
  );
}
