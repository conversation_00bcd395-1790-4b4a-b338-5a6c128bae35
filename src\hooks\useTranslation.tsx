import { useState, useCallback } from 'react';

// Interface para el resultado de traducción
export interface TranslationResult {
  original: string;
  translated: string;
  source: 'cache' | 'api' | 'predefined';
  loading?: boolean;
  error?: string;
}

// Interface para el caché de traducciones
interface TranslationCache {
  [key: string]: {
    translation: string;
    timestamp: number;
    fromLang: string;
    toLang: string;
  };
}

// Configuración del servicio de traducción
const TRANSLATION_CONFIG = {
  // LibreTranslate público (gratuito)
  API_URL: 'https://libretranslate.de/translate',
  // Caché válido por 24 horas
  CACHE_DURATION: 24 * 60 * 60 * 1000,
  // Clave para localStorage
  CACHE_KEY: 'storytale_translation_cache',
  // Timeout para requests
  REQUEST_TIMEOUT: 10000,
};

export const useTranslation = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Obtener caché del localStorage
  const getCache = useCallback((): TranslationCache => {
    try {
      const cached = localStorage.getItem(TRANSLATION_CONFIG.CACHE_KEY);
      return cached ? JSON.parse(cached) : {};
    } catch {
      return {};
    }
  }, []);

  // Guardar en caché
  const saveToCache = useCallback((
    key: string, 
    translation: string, 
    fromLang: string, 
    toLang: string
  ) => {
    try {
      const cache = getCache();
      cache[key] = {
        translation,
        timestamp: Date.now(),
        fromLang,
        toLang,
      };
      localStorage.setItem(TRANSLATION_CONFIG.CACHE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.warn('Error saving translation to cache:', error);
    }
  }, [getCache]);

  // Limpiar caché expirado
  const cleanExpiredCache = useCallback(() => {
    try {
      const cache = getCache();
      const now = Date.now();
      const cleaned: TranslationCache = {};
      
      Object.entries(cache).forEach(([key, value]) => {
        if (now - value.timestamp < TRANSLATION_CONFIG.CACHE_DURATION) {
          cleaned[key] = value;
        }
      });
      
      localStorage.setItem(TRANSLATION_CONFIG.CACHE_KEY, JSON.stringify(cleaned));
    } catch (error) {
      console.warn('Error cleaning cache:', error);
    }
  }, [getCache]);

  // Generar clave de caché
  const getCacheKey = useCallback((text: string, fromLang: string, toLang: string) => {
    return `${text.toLowerCase().trim()}_${fromLang}_${toLang}`;
  }, []);

  // Buscar en caché
  const findInCache = useCallback((
    text: string, 
    fromLang: string, 
    toLang: string
  ): string | null => {
    const cache = getCache();
    const key = getCacheKey(text, fromLang, toLang);
    const cached = cache[key];
    
    if (cached && Date.now() - cached.timestamp < TRANSLATION_CONFIG.CACHE_DURATION) {
      return cached.translation;
    }
    
    return null;
  }, [getCache, getCacheKey]);

  // Traducir usando API externa
  const translateWithAPI = useCallback(async (
    text: string,
    fromLang: string,
    toLang: string
  ): Promise<string> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TRANSLATION_CONFIG.REQUEST_TIMEOUT);

    try {
      const response = await fetch(TRANSLATION_CONFIG.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          source: fromLang,
          target: toLang,
          format: 'text',
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Translation API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.translatedText) {
        throw new Error('Invalid API response');
      }

      return data.translatedText;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Translation request timed out');
        }
        throw error;
      }
      
      throw new Error('Unknown translation error');
    }
  }, []);

  // Función principal de traducción
  const translateText = useCallback(async (
    text: string,
    fromLang: string = 'auto',
    toLang: string = 'es'
  ): Promise<TranslationResult> => {
    if (!text || text.trim().length === 0) {
      return {
        original: text,
        translated: text,
        source: 'cache',
        error: 'Empty text',
      };
    }

    setError(null);
    
    // Limpiar caché expirado periódicamente
    cleanExpiredCache();

    // Buscar en caché primero
    const cachedTranslation = findInCache(text, fromLang, toLang);
    if (cachedTranslation) {
      return {
        original: text,
        translated: cachedTranslation,
        source: 'cache',
      };
    }

    // Si no está en caché, usar API
    setIsLoading(true);
    
    try {
      const translation = await translateWithAPI(text, fromLang, toLang);
      
      // Guardar en caché
      saveToCache(getCacheKey(text, fromLang, toLang), translation, fromLang, toLang);
      
      setIsLoading(false);
      
      return {
        original: text,
        translated: translation,
        source: 'api',
      };
    } catch (error) {
      setIsLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Translation failed';
      setError(errorMessage);
      
      return {
        original: text,
        translated: text, // Fallback al texto original
        source: 'api',
        error: errorMessage,
      };
    }
  }, [cleanExpiredCache, findInCache, translateWithAPI, saveToCache, getCacheKey]);

  // Limpiar caché manualmente
  const clearCache = useCallback(() => {
    try {
      localStorage.removeItem(TRANSLATION_CONFIG.CACHE_KEY);
    } catch (error) {
      console.warn('Error clearing translation cache:', error);
    }
  }, []);

  return {
    translateText,
    isLoading,
    error,
    clearCache,
  };
};
