import React, { useEffect, useState } from "react";
import "./AppWelcomeScreen.scss";
import logo from "./favicon.png"

interface AppWelcomeScreenProps {
  onFinish?: () => void;
  minDuration?: number; // en ms
}

const AppWelcomeScreen: React.FC<AppWelcomeScreenProps> = ({ onFinish, minDuration = 5000 }) => {
  const [visible, setVisible] = useState(true);
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    setAnimate(true); // Trigger entrance animation
    const timer = setTimeout(() => {
      setAnimate(false); // Trigger exit animation
      setTimeout(() => {
        setVisible(false);
        if (onFinish) onFinish();
      }, 800); // Duration of exit animation
    }, minDuration);
    return () => clearTimeout(timer);
  }, [minDuration, onFinish]);

  if (!visible) return null;

  return (
    <div className={`app-welcome-screen modern-animate-bg ${animate ? "enter" : "exit"}`}>
      <div className={`welcome-logo-wrapper modern-animate-logo ${animate ? "enter" : "exit"}`}>
        <img src={logo} alt="Storytale Logo" className="welcome-logo animated-logo" />
      </div>
      <h2 className={`welcome-title modern-animate-title ${animate ? "enter" : "exit"}`}>Bienvenido a <span>Storytale App</span></h2>
      <p className={`welcome-subtitle modern-animate-subtitle ${animate ? "enter" : "exit"}`}>Aprende leyendo y escuchando</p>
      <div className="welcome-bubble welcome-bubble-1 modern-bubble-float" />
      <div className="welcome-bubble welcome-bubble-2 modern-bubble-float" />
      <div className="welcome-bubble welcome-bubble-3 modern-bubble-float" />
      <div className="welcome-bubble welcome-bubble-4 modern-bubble-float" />
      <div className="welcome-bubble welcome-bubble-5 modern-bubble-float" />
      <div className="welcome-bubble welcome-bubble-6 modern-bubble-float" />
    </div>
  );
};

export default AppWelcomeScreen;
