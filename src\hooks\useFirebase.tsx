import { createContext, useContext, useRef } from "react";
import { initializeApp, FirebaseApp } from "firebase/app";

export interface FirebaseContextType {
  app: FirebaseApp;
}

const firebaseContext = createContext<FirebaseContextType | null>(null);

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

export const FirebaseProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const appRef = useRef<FirebaseApp>(initializeApp(firebaseConfig));
  const app = appRef.current;

  return (
    <firebaseContext.Provider value={{ app }}>
      {children}
    </firebaseContext.Provider>
  );
};

export const useFirebase = () => {
  const context = useContext(firebaseContext);
  if (!context) throw Error("Firebase context no initialized");
  return context;
};
