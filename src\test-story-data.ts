// Ejemplo de cómo formatear los datos de historia para el nuevo sistema de traducción
// Basado en los datos exactos que proporcionaste

import { IStorytale } from './common/models/storytale.interface';
import { processStoryData, currentStoryData } from './utils/storyDataProcessor';

// Usar los datos exactos tal como se están renderizando
export const sampleStoryWithTranslations: IStorytale = processStoryData(currentStoryData);

// También exportar los datos raw para referencia
export const rawStoryData = currentStoryData;

/*
CÓMO USAR EL NUEVO SISTEMA CON TUS DATOS ACTUALES:

1. **Estructura de Datos**: 
   - Tus datos vienen en formato {"English": [["frase"]], "Spanish": [["frase"]]}
   - La función processStoryData los convierte al formato IStorytale

2. **Traducciones Limpias**: 
   - Las traducciones se muestran solo con letras y espacios
   - Se remueve puntuación innecesaria automáticamente

3. **Frases Completas**: 
   - Cada frase inglesa se empareja con su traducción española
   - Las frases aparecen completamente subrayadas y clickeables
   - Al hacer click, siempre se muestra la traducción limpia

4. **Experiencia Visual**:
   - Frases completas tienen fondo sutil y están completamente subrayadas
   - Hover effects mejorados para mejor feedback visual
   - Tooltips con traducción limpia y fuente identificada

FORMATO DE SALIDA:
- content: "Eri was a hedgehog.\n\nHe lived in a dark forest.\n\n..."
- translatedPhrases: [
    { original: "Eri was a hedgehog.", translated: "Eri era un erizo" },
    { original: "He lived in a dark forest.", translated: "Vivía en un bosque oscuro" },
    ...
  ]

Para usar con nuevos datos:
```typescript
import { processStoryData } from './utils/storyDataProcessor';

const newData = {
  "English": [["Nueva frase en inglés."], ["Otra frase."]],
  "Spanish": [["Nueva frase en español."], ["Otra frase."]]
};

const story = processStoryData(newData, "Nuevo Título", "Intermediate");
```
*/
