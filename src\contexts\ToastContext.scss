/* ===== ESTILOS PARA TOAST EN LA PARTE INFERIOR ===== */

.custom-bottom-toast {
  /* Posicionamiento desde abajo */
  bottom: 100px !important;
  position: fixed !important;
  z-index: 20000 !important;
  
  /* Estilos visuales modernos */
  --background: #fff;
  --color: #222;
  --border-radius: 16px;
  --box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
  --min-width: 220px;
  --max-width: 90vw;
  --padding-start: 20px;
  --padding-end: 20px;
  --font-size: 1rem;
  --font-weight: 500;
  --letter-spacing: 0.01em;
  
  border: 1.5px solid #e0e0e0;
  margin-bottom: 100px;
}

/* Estilos para diferentes tipos de toast */
.custom-bottom-toast.toast-success {
  --background: linear-gradient(90deg, #e0ffe7 0%, #b6f5c6 100%);
  --color: #217a3a;
  border-color: #b6f5c6;
  box-shadow: 0 4px 24px 0 rgba(34, 122, 58, 0.10);
}

.custom-bottom-toast.toast-danger {
  --background: linear-gradient(90deg, #ffe0e0 0%, #ffb6b6 100%);
  --color: #a32121;
  border-color: #ffb6b6;
  box-shadow: 0 4px 24px 0 rgba(163, 33, 33, 0.10);
}

.custom-bottom-toast.toast-warning {
  --background: linear-gradient(90deg, #fff3cd 0%, #ffeaa7 100%);
  --color: #856404;
  border-color: #ffeaa7;
  box-shadow: 0 4px 24px 0 rgba(133, 100, 4, 0.10);
}

.custom-bottom-toast.toast-primary {
  --background: linear-gradient(90deg, #cce7ff 0%, #a6d5fa 100%);
  --color: #004085;
  border-color: #a6d5fa;
  box-shadow: 0 4px 24px 0 rgba(0, 64, 133, 0.10);
}

/* Responsive design */
@media (max-width: 768px) {
  .custom-bottom-toast {
    --font-size: 0.95rem;
    --padding-start: 16px;
    --padding-end: 16px;
    --min-width: 180px;
    bottom: 80px !important;
    margin-bottom: 80px;
  }
}

@media (max-width: 480px) {
  .custom-bottom-toast {
    --font-size: 0.9rem;
    --padding-start: 12px;
    --padding-end: 12px;
    --min-width: 140px;
    bottom: 60px !important;
    margin-bottom: 60px;
    --max-width: 95vw;
  }
}

/* Animaciones de entrada y salida */
.custom-bottom-toast {
  animation: slideUpIn 0.3s ease-out;
}

@keyframes slideUpIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Mejorar la legibilidad del texto */
.custom-bottom-toast .toast-message {
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1.4;
}

/* Estilos para el botón de cerrar */
.custom-bottom-toast .toast-button {
  --color: inherit;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.3px;
}
