import React from "react";
import { IonSkeletonText } from "@ionic/react";
import "./SkeletonList.scss";

export default function SkeletonList({ count = 4 }) {
  return (
    <div className="skeleton-cards-grid">
      {[...Array(count)].map((_, idx) => (
        <div className="skeleton-card" key={idx}>
          <div className="skeleton-card-img">
            <IonSkeletonText
              animated={true}
              style={{ width: "100%", height: 120, borderRadius: 12 }}
            />
          </div>
          <div className="skeleton-card-content">
            <div className="skeleton-card-title">
              <IonSkeletonText
                animated={true}
                style={{ width: "80%", height: 18 }}
              />
            </div>
            <div className="skeleton-card-desc">
              <IonSkeletonText
                animated={true}
                style={{ width: "100%", height: 12, marginBottom: 6 }}
              />
              <IonSkeletonText
                animated={true}
                style={{ width: "60%", height: 12 }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
