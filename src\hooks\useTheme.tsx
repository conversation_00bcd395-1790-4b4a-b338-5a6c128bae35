import { useEffect, useState } from "react";

const THEME_KEY = "app-theme";

export type Theme = "light" | "dark";

export function useTheme() {
  // Detecta el tema inicial: localStorage > light por defecto
  const getInitialTheme = (): Theme => {
    const saved = localStorage.getItem(THEME_KEY) as Theme | null;
    if (saved === "light" || saved === "dark") return saved;
    // Siempre usar light como tema por defecto, ignorando la preferencia del SO
    return "light";
  };

  const [theme, setTheme] = useState<Theme>(getInitialTheme);

  useEffect(() => {
    document.body.classList.toggle("dark-theme", theme === "dark");
    localStorage.setItem(THEME_KEY, theme);
  }, [theme]);

  const toggleTheme = () => setTheme((t) => (t === "light" ? "dark" : "light"));

  return { theme, setTheme, toggleTheme };
}
