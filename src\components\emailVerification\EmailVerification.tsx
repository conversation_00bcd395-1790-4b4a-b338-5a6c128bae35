import {
  IonButton,
  IonContent,
  IonIcon,
  IonPage,
  IonText,
} from "@ionic/react";
import { mailOutline, bookOutline, sendOutline } from "ionicons/icons";
import "./EmailVerification.scss";
import logoBlanco from "../../assets/logo-blanco.png";
import { useLocation } from "react-router";
import { useState, useEffect, useRef, useMemo } from "react";
import { useAuth } from "../../hooks/useAuth";
import { sendEmailVerification } from "firebase/auth";
import useToast from "../../hooks/useToast";

interface LocationState {
  email?: string;
}

const EmailVerification: React.FC = () => {
  const location = useLocation<LocationState>();
  const { firebaseUser, originalUser } = useAuth();
  const { showSuccess, showError } = useToast();
  const [logoError, setLogoError] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const email = location.state?.email || firebaseUser?.email || "tu correo";

  // Función para limpiar el interval
  const clearCountdownInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };



  // Función para iniciar el countdown
  const startCountdown = (seconds: number) => {
    clearCountdownInterval(); // Limpiar cualquier interval existente
    setCountdown(seconds);

    intervalRef.current = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearCountdownInterval();
          return 0;
        }
        return prevCount - 1;
      });
    }, 1000);
  };

  // Limpiar interval al desmontar el componente
  useEffect(() => {
    return () => clearCountdownInterval();
  }, []);

  // Memoizar el texto del botón para evitar re-renders innecesarios
  const buttonText = useMemo(() => {
    if (countdown > 0) {
      return `Reenviar en ${countdown > 60 ? Math.ceil(countdown / 60) + 'm' : countdown + 's'}`;
    }
    if (isResending) {
      return "Enviando...";
    }
    return "Reenviar Email";
  }, [countdown, isResending]);

  // Memoizar el contenido estático para evitar re-renders
  const staticContent = useMemo(() => (
    <>
      {/* Elementos decorativos modernos */}
      <div className="bg-decoration decoration-1"></div>
      <div className="bg-decoration decoration-2"></div>
      <div className="bg-decoration decoration-3"></div>

      <div className="email-verification-container">
        <div className="verification-logo">
          {logoError ? (
            <div className="fallback-logo">
              <IonIcon icon={bookOutline} />
            </div>
          ) : (
            <img
              src={logoBlanco}
              alt="Story App Logo"
              onError={() => setLogoError(true)}
            />
          )}
          <h1>Story App</h1>
        </div>

        <div className="verification-card">
          <div className="verification-icon">
            <IonIcon icon={mailOutline} />
          </div>

          <h2 className="verification-title">Verificación de Email</h2>

          <div className="verification-content">
            <IonText>
              <p className="verification-message">
                Hemos enviado un correo de verificación a:
              </p>
              <p className="email-address">{email}</p>
              <p className="verification-instructions">
                Por favor, revisa tu bandeja de entrada y haz clic en el enlace
                de verificación para activar tu cuenta.
              </p>
              <p className="verification-note">
                Si no encuentras el correo, revisa tu carpeta de spam.
              </p>
            </IonText>
          </div>
        </div>
      </div>
    </>
  ), [logoError, email]);

  const handleResendEmail = async () => {
    if (!originalUser || countdown > 0) return;

    setIsResending(true);
    try {
      await sendEmailVerification(originalUser);
      showSuccess("Correo de verificación enviado exitosamente");
      startCountdown(60); // 60 segundos de espera
    } catch (error: any) {
      let errorMessage = "Error al enviar el correo";

      if (error.code === "auth/too-many-requests" || error.message?.includes("TOO_MANY_ATTEMPTS")) {
        errorMessage = "Has enviado demasiados correos. Intenta nuevamente en 1 hora.";
        startCountdown(3600); // 1 hora de espera para este error específico
      } else {
        errorMessage = "Error al enviar el correo: " + (error.message || "Intenta nuevamente");
      }

      showError(errorMessage);
    } finally {
      setIsResending(false);
    }
  };



  return (
    <IonPage>
      <IonContent className="email-verification-page">
        {staticContent}

        <IonButton
          expand="block"
          className="resend-email-button"
          onClick={handleResendEmail}
          disabled={countdown > 0 || isResending}
        >
          <IonIcon icon={sendOutline} slot="start" />
          {buttonText}
        </IonButton>
      </IonContent>
    </IonPage>
  );
};

export default EmailVerification;
