import {
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonCardTitle,
  IonIcon,
  IonChip,
  IonLabel,
  IonBadge,
  IonActionSheet,
  IonAlert,
} from "@ionic/react";
import {
  bookOutline,
  languageOutline,
  timeOutline,
  chevronForwardOutline,
  ellipsisVertical,
  trash,
} from "ionicons/icons";
import React, { useState } from "react";
import "./Card.scss";

export interface ICardGalleryProps {
  id?: number;
  title: string;
  laguage: string;
  genre?: string;
  difficulty?: string;
  createdAt?: string;
  onClick?: () => void;
  onDelete?: (id?: number) => void;
  isDeleting?: boolean; 
}

export default function CardGallery({
  id,
  title,
  laguage,
  genre,
  difficulty,
  createdAt,
  onClick,
  onDelete,
  isDeleting,
}: ICardGalleryProps) {
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);

  // Función para formatear la fecha
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Recently";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch (e) {
      return "Recently";
    }
  };

  // Función para obtener el color del nivel de dificultad
  const getDifficultyColor = (level?: string) => {
    if (!level) return "medium";

    switch (level.toLowerCase()) {
      case "easy":
        return "success";
      case "medium":
        return "warning";
      case "hard":
        return "danger";
      default:
        return "medium";
    }
  };

  // Función para obtener la inicial del título
  const getInitial = (title: string) => {
    return title.charAt(0).toUpperCase();
  };

  return (
    <IonCard className={`story-card${isDeleting ? ' deleting' : ''}`}>
      <IonCardHeader>
        <div className="header-content">
          <div className="card-avatar">
            <div className="avatar-circle">{getInitial(title)}</div>
          </div>
          <IonCardTitle onClick={onClick} className="card-title">
            {title}
          </IonCardTitle>
          {/* Botón de opciones con trigger para ActionSheet */}
          <button
            className="button-delete"
            id={`open-action-sheet-${id}`}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // El trigger se encarga de abrir el ActionSheet
            }}
            disabled={isDeleting}
          >
            <IonIcon icon={ellipsisVertical} />
          </button>
        </div>
        <div className="card-meta">
          <IonChip outline={true} className="language-chip">
            <IonIcon icon={languageOutline} />
            <IonLabel>{laguage || "Unknown"}</IonLabel>
          </IonChip>

          {difficulty && (
            <IonBadge
              color={getDifficultyColor(difficulty)}
              className="difficulty-badge"
            >
              {difficulty}
            </IonBadge>
          )}
        </div>
      </IonCardHeader>
      {/* Hacemos clic solo en el contenido principal, no en el icono de opciones */}
      <div onClick={onClick} style={{ cursor: isDeleting ? "not-allowed" : "pointer", opacity: isDeleting ? 0.5 : 1 }}>
        <IonCardContent className="card-content">
          <div className="card-details">
            {genre && (
              <div className="detail-item">
                <IonIcon icon={bookOutline} />
                <span>{genre}</span>
              </div>
            )}

            <div className="detail-item">
              <IonIcon icon={timeOutline} />
              <span>{formatDate(createdAt)}</span>
            </div>
          </div>

          <div className="card-action">
            {isDeleting ? (
              <span className="deleting-text">Eliminando...</span>
            ) : (
              <>
                <span>Read</span>
                <IonIcon icon={chevronForwardOutline} />
              </>
            )}
          </div>
        </IonCardContent>
      </div>
      {/* ActionSheet para opciones usando trigger */}
      <IonActionSheet
        trigger={`open-action-sheet-${id}`}
        header="Opciones"
        onDidDismiss={() => setShowActionSheet(false)}
        buttons={[
          {
            text: "Eliminar",
            role: "destructive",
            icon: trash,
            handler: () => setShowDeleteAlert(true),
            cssClass: "action-sheet-delete",
            disabled: isDeleting,
          },
          {
            text: "Cancelar",
            role: "cancel",
          },
        ]}
      />
      {/* Alert de confirmación de borrado */}
      <IonAlert
        isOpen={showDeleteAlert}
        onDidDismiss={() => setShowDeleteAlert(false)}
        header="Eliminar historia"
        message="¿Estás seguro de que deseas eliminar esta historia?"
        buttons={[
          {
            text: "Cancelar",
            role: "cancel",
            handler: () => setShowDeleteAlert(false),
          },
          {
            text: "Eliminar",
            role: "destructive",
            handler: () => {
              setShowDeleteAlert(false);
              if (!isDeleting && onDelete) onDelete(id);
            },
          },
        ]}
      />
    </IonCard>
  );
}
