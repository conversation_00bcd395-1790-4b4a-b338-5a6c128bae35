import { Redirect, Route } from "react-router-dom";
import { IonApp, setupIonicReact } from "@ionic/react";
import { IonReactRouter } from "@ionic/react-router";

/* Core CSS required for Ionic components to work properly */
import "@ionic/react/css/core.css";

/* Basic CSS for apps built with Ionic */
import "@ionic/react/css/normalize.css";
import "@ionic/react/css/structure.css";
import "@ionic/react/css/typography.css";

/* Optional CSS utils that can be commented out */
import "@ionic/react/css/padding.css";
import "@ionic/react/css/float-elements.css";
import "@ionic/react/css/text-alignment.css";
import "@ionic/react/css/text-transformation.css";
import "@ionic/react/css/flex-utils.css";
import "@ionic/react/css/display.css";

// import "@ionic/react/css/palettes/dark.system.css"; // Comentado para usar nuestro sistema personalizado

/* Theme variables */
import "./theme/variables.scss";
import { AuthProvider, useAuth } from "./hooks/useAuth";
import { FirebaseProvider } from "./hooks/useFirebase";
import { useUser } from "./hooks/useUser";
import "./App.scss";
import "./theme/popover-fix.css";
import Tabs from "./components/tabs/Tabs";
import StoryReader from "./components/story-reader/StoryReader";
import StoryReaderDemo from "./components/StoryReaderDemo";
import Login from "./components/login/Login";
import { BusyProvider, useBusy } from "./hooks/useBusy";
import Spinner from "./components/spinners/Spinner";
import AppWelcomeScreen from "./components/appWelcomeScreen/AppWelcomeScreen";
import { useState, useEffect } from "react";
import { useTheme } from "./hooks/useTheme";
import CreateAccount from "./components/createAccount/CreateAccount";
import EmailVerification from "./components/emailVerification/EmailVerification";
import AuthGuard from "./components/authGuard/AuthGuard";
import { ToastProvider } from "./contexts/ToastContext";

setupIonicReact();

const _App: React.FC = () => {
  const { user } = useUser();
  const { loading } = useAuth();
  const { isBusy } = useBusy();
  const { theme } = useTheme();

  const [showWelcome, setShowWelcome] = useState(true);

  // Aplicar el tema al body cuando cambie
  useEffect(() => {
    document.body.classList.toggle("dark-theme", theme === "dark");
  }, [theme]);

  if (loading || showWelcome) {
    return (
      <AppWelcomeScreen
        onFinish={() => setShowWelcome(false)}
        minDuration={3000}
      />
    );
  }

  return (
    <IonApp>
      <IonReactRouter>
        {!user ? (
          <>
            <Route exact path="/login">
              <Login />
            </Route>
            <Route exact path="/register">
              <CreateAccount />
            </Route>
            <Route exact path="*">
              <Redirect to="/login" />
            </Route>
          </>
        ) : (
          <AuthGuard>
            <Route path="/tabs">
              <Tabs />
            </Route>
            <Route exact path="/email-verification">
              <EmailVerification />
            </Route>
            <Route path="/story-reader">
              <StoryReader />
            </Route>
            <Route exact path="/demo">
              <StoryReaderDemo />
            </Route>
            <Route exact path="/">
              <Redirect to="/tabs/home" />
            </Route>
            <Route exact path="*">
              <Redirect to="/tabs/home" />
            </Route>
          </AuthGuard>
        )}
      </IonReactRouter>
      {isBusy && <Spinner />}
    </IonApp>
  );
};

// Componente ThemeProvider simple
const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <>{children}</>;
};

const App = () => {
  return (
    <FirebaseProvider>
      <AuthProvider>
        <BusyProvider>
          <ToastProvider>
            <ThemeProvider>
              <_App />
            </ThemeProvider>
          </ToastProvider>
        </BusyProvider>
      </AuthProvider>
    </FirebaseProvider>
  );
};
export default App;
