import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonButton,
  IonChip,
  IonBadge
} from '@ionic/react';
import {
  person,
  mail,
  logOut,
  star,
  calendar,
  shield
} from 'ionicons/icons';
import { useUser } from '../../hooks/useUser';
import { useAuth } from '../../hooks/useAuth';
import './Profile.scss';
import { useEffect } from 'react';
import Header from '../header/Header';

export default function Profile() {
  const { user } = useUser();
  const { logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  useEffect(() => {console.log('Profile component mounted', user);
  }, []);

  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No disponible';
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'No disponible';
    }
  };

  // Helper function to get user initials
  const getUserInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <IonPage>
      <Header />
      <IonContent>
        <div className="profile-container">
          {/* Profile Header */}
          <div className="profile-header">
            <div className="profile-avatar">
              <div className="avatar-circle">
                {getUserInitials(user?.name)}
              </div>
              <div className="avatar-status">
                <IonIcon icon={shield} />
              </div>
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{user?.name || 'Usuario Invitado'}</h1>
              <p className="profile-email">{user?.email || 'Email no disponible'}</p>
              <div className="profile-stats">
                <IonChip className="stat-chip">
                  <IonIcon icon={star} />
                  <span>{user?.points || 0} Puntos</span>
                </IonChip>
                <IonBadge className="status-badge" color="success">Activo</IonBadge>
              </div>
            </div>
          </div>

          {/* User Information Card */}
          <IonCard className="info-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={person} />
                Detalles de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="info-grid">
                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={person} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Nombre Completo</span>
                    <span className="info-value">{user?.name || 'No disponible'}</span>
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={mail} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Dirección de Email</span>
                    <span className="info-value">{user?.email || 'No disponible'}</span>
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={calendar} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Última Actualización</span>
                    <span className="info-value">{formatDate(user?.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Actions Card */}
          <IonCard className="actions-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={logOut} />
                Acciones de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonButton
                className="action-button logout-btn"
                expand="block"
                fill="solid"
                color="danger"
                onClick={handleLogout}
              >
                <IonIcon icon={logOut} slot="start" />
                Cerrar Sesión
              </IonButton>
            </IonCardContent>
          </IonCard>
        </div>
      </IonContent>
    </IonPage>
  );
}
