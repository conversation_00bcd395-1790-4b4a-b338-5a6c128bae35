import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Firmar la aplicación con la respectiva Key
function signAAB(
  aabPath,
  keystorePath,
  keystoreAlias,
  keystorePassword,
) {
  execSync(
    `jarsigner -keystore ${keystorePath} -storepass ${keystorePassword} ${aabPath} ${keystoreAlias}`,
    { stdio: "inherit" }
  );
}

(async () => {
  try {

    // Sincronizar Capacitor
    execSync("npx cap copy android", { stdio: "inherit" });
    execSync("npx cap sync android", { stdio: "inherit" });
    console.log("Capacitor synchronized.");

    // execSync(`npx capacitor-assets generate --android --assetPath src/apps/${appName}/playstoreAssets`)

    // execSync("cat android/app/build.gradle", { stdio: "inherit" })

    /**Build aab */
    // execSync("./android/gradlew bundleRelease -p ./android --warning-mode all", {
    //   stdio: "inherit",
    // });

    /**Build Apk */
    execSync("./android/gradlew assembleDebug -p ./android --warning-mode all", {
      stdio: "inherit",
    });
    

    console.log("Build for Android App Bundle completed.");

    // Firmar el App Bundle generado
    const keystorePath = path.resolve(
      __dirname,
      `../keystore.jks`
    );
    const keystoreAlias = "storytale";
    const keystorePassword = "storytale1234";
    // const aabPath = path.resolve(
    //   __dirname,
    //   "../android/app/build/outputs/bundle/release/app-release.aab"
    // );

    const aabPath = path.resolve(
      __dirname,
      "../android/app/build/outputs/apk/app-debug.apk"
    );

    signAAB(
      aabPath,
      keystorePath,
      keystoreAlias,
      keystorePassword,
    );

    console.log("AAB signed.");

  } catch (error) {
    console.error("Error during build process:", error);
  } finally {
    // // Limpiar el archivo de configuración
    // if (fs.existsSync(destinationConfigPath)) {
    //   fs.unlinkSync(destinationConfigPath);
    //   console.log(`Removed temporary config file ${destinationConfigPath}`);
    // }
  }
})();
