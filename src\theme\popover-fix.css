/* Estilos globales para arreglar el fondo oscuro en los popovers */
ion-popover {
  --ion-background-color: var(--background-white) !important;
  --ion-text-color: var(--text-dark) !important;
  --ion-item-background: var(--background-white) !important;
  --background: var(--background-white) !important;
  border: none !important;
}

/* Eliminar bordes oscuros */
ion-popover::part(content) {
  border-top: none !important;
  border-bottom: none !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
}

/* Arreglar los items dentro del popover */
ion-popover ion-item {
  --background: var(--background-white) !important;
  --color: var(--text-dark) !important;
  --background-hover: var(--background-light) !important;
  --background-hover-opacity: 1 !important;
  --ripple-color: var(--primary-light) !important;
  --border-color: transparent !important;
}

/* Estilo para el último item (logout) */
ion-popover ion-item:last-child {
  --color: var(--error-color) !important;
  border-top: 1px solid var(--border-color) !important;
  margin-top: 5px !important;
}

/* Arreglar las opciones del select */
.select-interface-option {
  --color: var(--text-dark) !important;
  background-color: var(--background-white) !important;
  transition: background-color 0.2s ease !important;
}

/* Efecto hover para las opciones */
.select-interface-option:hover {
  background-color: var(--background-light) !important;
  opacity: 1 !important;
}

/* Quitar el highlight de la primera opción */
.select-interface-option.select-interface-option-selected {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
  font-weight: normal !important;
}

/* Solo cuando se hace hover, cambiar el fondo */
.select-interface-option.select-interface-option-selected:hover {
  background-color: var(--background-light) !important;
}

/* Arreglar los action sheets */
.action-sheet-button.sc-ion-action-sheet-md,
.action-sheet-button.sc-ion-action-sheet-ios {
  color: var(--text-dark) !important;
}

.action-sheet-group.sc-ion-action-sheet-md,
.action-sheet-group.sc-ion-action-sheet-ios {
  background: var(--background-white) !important;
}
