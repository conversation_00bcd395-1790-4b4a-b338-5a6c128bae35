/* ===== EMAIL VERIFICATION MODERNO Y RESPONSIVO ===== */

/* Sobrescribir estilos globales de ion-content específicamente para EmailVerification */
.email-verification-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 0 20px;
  
  /* Elementos decorativos modernos */
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
}

/* Elementos decorativos adicionales */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  pointer-events: none;
  z-index: 0;
  
  &.decoration-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -5%;
    animation: float 6s ease-in-out infinite;
  }
  
  &.decoration-2 {
    width: 150px;
    height: 150px;
    bottom: 15%;
    right: -10%;
    animation: float 8s ease-in-out infinite reverse;
  }
  
  &.decoration-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    left: 85%;
    animation: float 7s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* Sobrescribir ion-content específicamente para el componente EmailVerification */
ion-content.email-verification-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* Regla más específica para sobrescribir completamente los estilos globales */
ion-page ion-content.email-verification-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* ===== CONTENEDOR PRINCIPAL ===== */
.email-verification-container {
  width: 100%;
  max-width: 450px;
  padding: 80px 0 20px 0;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: calc(100vh - 100px);
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  gap: 30px;
}

/* ===== LOGO Y HEADER ===== */
.verification-logo {
  text-align: center;
  flex-shrink: 0;
  z-index: 2;
  position: relative;

  img, .fallback-logo {
    width: 90px;
    height: 90px;
    object-fit: contain;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    padding: 18px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
  }

  .fallback-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    
    ion-icon {
      font-size: 45px;
      color: #ffffff;
    }
  }

  h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* ===== TARJETA DE VERIFICACIÓN ===== */
.verification-card {
  width: 100%;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px 35px;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(20px);
  flex-shrink: 0;
  text-align: center;

  /* Borde superior decorativo */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #4a6fa5;
    z-index: 3;
  }

  .verification-icon {
    margin: 0 auto 20px auto;
    display: flex;
    justify-content: center;

    ion-icon {
      font-size: 4rem;
      color: #4a6fa5;
      background: rgba(74, 111, 165, 0.1);
      border-radius: 50%;
      padding: 20px;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .verification-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d4373;
    margin: 0 0 30px 0;
    letter-spacing: 0.3px;
  }
}

/* ===== CONTENIDO DE VERIFICACIÓN ===== */
.verification-content {
  margin-bottom: 35px;

  .success-icon {
    margin-bottom: 20px;
    
    ion-icon {
      font-size: 3rem;
      color: #28a745;
    }
  }

  .verification-message {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 15px;
    font-weight: 500;
    letter-spacing: 0.3px;
  }

  .email-address {
    font-size: 1.2rem;
    color: #4a6fa5;
    font-weight: 700;
    margin-bottom: 20px;
    padding: 12px 20px;
    background: rgba(74, 111, 165, 0.1);
    border-radius: 12px;
    border-left: 4px solid #4a6fa5;
    letter-spacing: 0.5px;
  }

  .verification-instructions {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.6;
    letter-spacing: 0.2px;
  }

  .verification-note {
    font-size: 0.9rem;
    color: #868e96;
    font-style: italic;
    margin-bottom: 0;
    letter-spacing: 0.2px;
  }
}

/* ===== BOTÓN DE REENVÍO ===== */
.resend-email-button {
  background: #4a6fa5 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(74, 111, 165, 0.3) !important;
  padding: 16px 24px !important;
  margin: 0px 20px 40px 20px !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  letter-spacing: 0.5px !important;
  text-transform: none !important;
  position: relative !important;
  z-index: 2 !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /* Asegurar que el botón nativo tenga los estilos */
  .button-native {
    background: #4a6fa5 !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 24px rgba(74, 111, 165, 0.3) !important;
    padding: 16px 24px !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    letter-spacing: 0.5px !important;
    text-transform: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Efecto de brillo sutil */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    z-index: 1;
  }

  &:not([disabled]):hover::before {
    left: 100%;
  }

  &:not([disabled]):hover {
    background: #3d5a8a !important;
    box-shadow: 0 12px 32px rgba(74, 111, 165, 0.4) !important;
    transform: translateY(-2px) !important;

    .button-native {
      background: #3d5a8a !important;
      box-shadow: 0 12px 32px rgba(74, 111, 165, 0.4) !important;
    }
  }

  &:not([disabled]):active {
    background: #3d5a8a !important;
    transform: translateY(0px) !important;
    box-shadow: 0 6px 16px rgba(74, 111, 165, 0.3) !important;

    .button-native {
      background: #3d5a8a !important;
      box-shadow: 0 6px 16px rgba(74, 111, 165, 0.3) !important;
    }
  }

  /* Estado deshabilitado */
  &[disabled] {
    background: rgba(74, 111, 165, 0.4) !important;
    color: rgba(255, 255, 255, 0.7) !important;
    box-shadow: 0 4px 12px rgba(74, 111, 165, 0.15) !important;
    cursor: not-allowed !important;

    .button-native {
      background: rgba(74, 111, 165, 0.4) !important;
      color: rgba(255, 255, 255, 0.7) !important;
      box-shadow: 0 4px 12px rgba(74, 111, 165, 0.15) !important;
    }

    ion-icon {
      opacity: 0.7 !important;
    }
  }

  /* Icono del botón */
  ion-icon {
    font-size: 1.2rem !important;
    margin-right: 8px !important;
    transition: transform 0.3s ease !important;
    color: #ffffff !important;
  }

  &:not([disabled]):hover ion-icon {
    transform: scale(1.1) !important;
  }
}

/* ===== ANIMACIONES ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.email-verification-container {
  animation: fadeIn 0.8s ease-out;
}

.verification-card {
  animation: fadeIn 1s ease-out 0.2s both;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablets */
@media (max-width: 768px) and (min-width: 481px) {
  .email-verification-page {
    padding: 0 20px;
  }

  .email-verification-container {
    max-width: 400px;
    gap: 25px;
    padding: 60px 0 20px 0;
    min-height: calc(100vh - 80px);
  }

  .verification-logo {
    img, .fallback-logo {
      width: 80px;
      height: 80px;
      padding: 15px;
    }

    .fallback-logo ion-icon {
      font-size: 40px;
    }

    h1 {
      font-size: 1.6rem;
    }
  }

  .verification-card {
    padding: 35px 30px;

    .verification-title {
      font-size: 1.6rem;
      margin-bottom: 25px;
    }

    .verification-icon ion-icon {
      font-size: 3.5rem;
      width: 70px;
      height: 70px;
      padding: 15px;
    }
  }

  .verification-content {
    margin-bottom: 30px;

    .verification-message {
      font-size: 1rem;
    }

    .email-address {
      font-size: 1.1rem;
    }

    .verification-instructions {
      font-size: 0.95rem;
    }
  }

  /* Botón responsivo para tablets */
  .resend-email-button {
    margin: 0px 15px 35px 15px !important;
    font-size: 1rem !important;
    padding: 14px 20px !important;
    border-radius: 14px !important;

    .button-native {
      padding: 14px 20px !important;
      border-radius: 14px !important;
      font-size: 1rem !important;
    }
  }
}

/* Móviles */
@media (max-width: 480px) {
  .email-verification-page {
    padding: 0 20px;
  }

  .email-verification-container {
    max-width: 100%;
    gap: 20px;
    min-height: calc(100vh - 60px);
    justify-content: flex-start;
    padding: 40px 0 20px 0;
  }

  .verification-logo {
    img, .fallback-logo {
      width: 70px;
      height: 70px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .fallback-logo ion-icon {
      font-size: 35px;
    }

    h1 {
      font-size: 1.5rem;
    }
  }

  .verification-card {
    padding: 30px 25px;
    border-radius: 20px;
    margin: 0 15px;

    .verification-title {
      font-size: 1.4rem;
      margin-bottom: 20px;
    }

    .verification-icon {
      margin: 0 auto 15px auto;
      display: flex;
      justify-content: center;

      ion-icon {
        font-size: 3rem;
        width: 60px;
        height: 60px;
        padding: 12px;
      }
    }
  }

  .verification-content {
    margin-bottom: 25px;

    .success-icon ion-icon {
      font-size: 2.5rem;
    }

    .verification-message {
      font-size: 0.95rem;
      margin-bottom: 12px;
    }

    .email-address {
      font-size: 1rem;
      padding: 10px 15px;
      margin-bottom: 15px;
    }

    .verification-instructions {
      font-size: 0.9rem;
      margin-bottom: 12px;
    }

    .verification-note {
      font-size: 0.85rem;
    }
  }

  /* Botón responsivo para móviles */
  .resend-email-button {
    margin: 0px 15px 30px 15px !important;
    font-size: 0.95rem !important;
    padding: 12px 20px !important;
    border-radius: 12px !important;
    box-shadow: 0 6px 20px rgba(74, 111, 165, 0.25) !important;

    .button-native {
      padding: 12px 20px !important;
      border-radius: 12px !important;
      font-size: 0.95rem !important;
      box-shadow: 0 6px 20px rgba(74, 111, 165, 0.25) !important;
    }

    ion-icon {
      font-size: 1.1rem !important;
      margin-right: 6px !important;
    }
  }
}

/* Móviles muy pequeños */
@media (max-width: 360px) {
  .email-verification-page {
    padding: 0 15px;
  }

  .email-verification-container {
    gap: 18px;
    padding: 30px 0 20px 0;
    min-height: calc(100vh - 50px);
  }

  .verification-logo {
    img, .fallback-logo {
      width: 60px;
      height: 60px;
      padding: 10px;
      margin-bottom: 12px;
    }

    .fallback-logo ion-icon {
      font-size: 30px;
    }

    h1 {
      font-size: 1.3rem;
    }
  }

  .verification-card {
    padding: 25px 20px;
    border-radius: 18px;
    margin: 0 12px;

    .verification-title {
      font-size: 1.3rem;
      margin-bottom: 18px;
    }

    .verification-icon {
      margin: 0 auto 12px auto;
      display: flex;
      justify-content: center;

      ion-icon {
        font-size: 2.5rem;
        width: 50px;
        height: 50px;
        padding: 10px;
      }
    }
  }

  .verification-content {
    margin-bottom: 20px;

    .success-icon ion-icon {
      font-size: 2rem;
    }

    .verification-message {
      font-size: 0.9rem;
      margin-bottom: 10px;
    }

    .email-address {
      font-size: 0.95rem;
      padding: 8px 12px;
      margin-bottom: 12px;
    }

    .verification-instructions {
      font-size: 0.85rem;
      margin-bottom: 10px;
    }

    .verification-note {
      font-size: 0.8rem;
    }
  }

  /* Botón responsivo para móviles muy pequeños */
  .resend-email-button {
    margin: 0px 12px 25px 12px !important;
    font-size: 0.9rem !important;
    padding: 10px 18px !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 16px rgba(74, 111, 165, 0.2) !important;

    .button-native {
      padding: 10px 18px !important;
      border-radius: 10px !important;
      font-size: 0.9rem !important;
      box-shadow: 0 4px 16px rgba(74, 111, 165, 0.2) !important;
    }

    ion-icon {
      font-size: 1rem !important;
      margin-right: 5px !important;
    }
  }
}
