# 📚 StoryTale Application

<p align="center">
  <img src="public/assets/icon/icon.png" alt="StoryTale Logo" width="120">
</p>

<p align="center">
  <strong>Una aplicación móvil moderna para crear, leer y disfrutar historias interactivas</strong>
</p>

<p align="center">
  <img src="https://img.shields.io/badge/React-18.2.0-blue?style=flat-square&logo=react" alt="React">
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript" alt="TypeScript">
  <img src="https://img.shields.io/badge/Ionic-7.0-blue?style=flat-square&logo=ionic" alt="Ionic">
  <img src="https://img.shields.io/badge/Firebase-10.0-orange?style=flat-square&logo=firebase" alt="Firebase">
</p>

StoryTale es una aplicación móvil elegante y moderna que transforma la experiencia de lectura digital. Con un diseño cuidadosamente crafteado, funciones avanzadas de texto a voz, y una interfaz completamente adaptable entre temas claro y oscuro, StoryTale ofrece una experiencia premium para los amantes de la literatura.

## ✨ Características Principales

### 📖 **Experiencia de Lectura Premium**
- **StoryReader Moderno**: Interfaz de lectura elegante con tipografía optimizada
- **Espaciado Perfecto**: Texto con espaciado entre letras y líneas para máxima legibilidad
- **Controles de Audio**: Reproducción de texto a voz con controles intuitivos
- **Diseño Sin Distracciones**: Interfaz limpia que se enfoca en el contenido

### 🎨 **Sistema de Temas Avanzado**
- **Tema Claro/Oscuro**: Cambio fluido entre temas con un solo clic
- **Adaptación Automática**: Todos los componentes se adaptan perfectamente
- **Colores Consistentes**: Paleta de colores cuidadosamente diseñada
- **Persistencia**: El tema seleccionado se guarda automáticamente

### 🔍 **Sistema de Filtros Inteligente**
- **Filtros Compactos**: Interfaz optimizada que ahorra espacio
- **Múltiples Criterios**: Filtra por dificultad, idioma y género
- **Contador en Tiempo Real**: Muestra resultados filtrados instantáneamente
- **Interfaz en Español**: Completamente localizada

### 📱 **Diseño Responsive Moderno**
- **Mobile-First**: Optimizado para dispositivos móviles
- **Adaptación Perfecta**: Funciona en tablets y desktop
- **Componentes Elegantes**: Diseño moderno y profesional
- **Animaciones Suaves**: Transiciones fluidas y naturales

### 🎯 **Funcionalidades Adicionales**
- **Pantalla de Bienvenida**: Diseño elegante con animaciones
- **Skeletons Adaptativos**: Carga visual mejorada para ambos temas
- **Autenticación Segura**: Sistema de login con Firebase
- **Galería de Cuentos**: Explora historias con filtros avanzados

## 🛠️ Stack Tecnológico

### **Frontend**
- **React 18.2.0** - Biblioteca de UI moderna
- **TypeScript 5.0** - Tipado estático para mayor robustez
- **Ionic Framework 7.0** - Componentes móviles nativos
- **Vite** - Build tool ultrarrápido
- **SCSS** - Preprocesador CSS avanzado

### **Backend & Servicios**
- **Firebase Authentication** - Autenticación segura
- **Firebase Storage** - Almacenamiento en la nube
- **Firebase Firestore** - Base de datos NoSQL

### **Funcionalidades Nativas**
- **Capacitor** - Bridge nativo para Android/iOS
- **Text-to-Speech Plugin** - Síntesis de voz
- **Local Storage** - Persistencia de preferencias

### **Herramientas de Desarrollo**
- **ESLint** - Linting de código
- **Prettier** - Formateo automático
- **Vitest** - Testing framework
- **TypeScript Strict Mode** - Máxima seguridad de tipos

## 📋 Requisitos Previos

- Node.js **v22.11.0**
- Ionic CLI instalado globalmente (`npm install -g @ionic/cli`)
- npm o yarn
- Android Studio (para desarrollo en Android)
- Xcode (para desarrollo en iOS)
- Firebase cuenta y proyecto configurado

## 🔧 Instalación

1. Clona el repositorio:
   ```bash
   git clone https://github.com/tu-usuario/story_application_frontend.git
   cd story_application_frontend
   ```

2. Instala las dependencias:
   ```bash
   npm install
   ```

3. Configura las variables de entorno:
   Crea un archivo `.env` en la raíz del proyecto con las siguientes variables:
   ```
   VITE_API_URL=http://localhost:3000
   VITE_FIREBASE_API_KEY=tu-api-key
   VITE_FIREBASE_AUTH_DOMAIN=tu-auth-domain
   VITE_FIREBASE_PROJECT_ID=tu-project-id
   VITE_FIREBASE_STORAGE_BUCKET=tu-storage-bucket
   VITE_FIREBASE_MESSAGING_SENDER_ID=tu-messaging-sender-id
   VITE_FIREBASE_APP_ID=tu-app-id
   VITE_FIREBASE_MEASUREMENT_ID=tu-measurement-id
   ```

4. Inicia el servidor de desarrollo:
   ```bash
   npm run dev
   ```

## 📱 Compilación para Android

1. Construye la aplicación:
   ```bash
   npm run build
   ```

2. Sincroniza con Capacitor:
   ```bash
   npx cap sync android
   ```

3. Abre el proyecto en Android Studio:
   ```bash
   npx cap open android
   ```

4. Construye y ejecuta la aplicación desde Android Studio

## 🧪 Pruebas

Para ejecutar las pruebas unitarias:
```bash
npm run test.unit
```

Para ejecutar las pruebas end-to-end:
```bash
npm run test.e2e
```

## 📂 Estructura del Proyecto

```
story_application_frontend/
├── android/                    # Código nativo de Android
├── public/                     # Archivos públicos y assets
│   └── assets/icon/           # Iconos de la aplicación
├── src/
│   ├── assets/                # Recursos estáticos
│   ├── common/                # Utilidades y modelos compartidos
│   │   └── models/           # Interfaces TypeScript
│   ├── components/            # Componentes reutilizables
│   │   ├── appWelcomeScreen/ # Pantalla de bienvenida moderna
│   │   ├── config-story-reader/ # Configuración del lector
│   │   ├── skelentons/       # Componentes de carga
│   │   ├── story-reader/     # Lector de historias elegante
│   │   └── storytale-list/   # Lista con filtros avanzados
│   ├── hooks/                 # Custom hooks
│   │   └── useTheme.ts       # Hook para manejo de temas
│   ├── layout/                # Componentes de layout
│   ├── pages/                 # Páginas principales
│   │   ├── home/             # Página de inicio
│   │   ├── profile/          # Perfil de usuario
│   │   └── story-reader/     # Lector de historias
│   ├── theme/                 # Sistema de temas
│   │   └── variables.scss    # Variables CSS para temas
│   ├── App.tsx               # Componente raíz
│   └── main.tsx              # Punto de entrada
├── .env                       # Variables de entorno
├── capacitor.config.ts        # Configuración de Capacitor
├── package.json              # Dependencias y scripts
├── vite.config.ts            # Configuración de Vite
└── README.md                 # Documentación del proyecto
```

## 🎨 Componentes Destacados

### **StoryReader**
Componente de lectura premium con:
- Tipografía optimizada para lectura
- Controles de audio elegantes
- Soporte completo para temas
- Diseño responsive moderno

### **StorytaleList**
Lista inteligente con:
- Filtros compactos y eficientes
- Contador de resultados centrado
- Interfaz completamente en español
- Skeletons adaptativos por tema

### **AppWelcomeScreen**
Pantalla de bienvenida con:
- Animaciones fluidas
- Diseño moderno con gradientes
- Logo adaptado para transparencia
- Efectos visuales elegantes

## 🎯 Mejoras de UX/UI Implementadas

### **🌙 Sistema de Temas**
- **Toggle intuitivo**: Botón de tema accesible en toda la aplicación
- **Transiciones suaves**: Cambios de tema sin parpadeos
- **Persistencia**: Preferencia guardada automáticamente
- **Consistencia**: Todos los componentes adaptan colores perfectamente

### **📖 Experiencia de Lectura**
- **Tipografía mejorada**: Espaciado optimizado entre letras y líneas
- **Sin distracciones**: Eliminación de efectos hover molestos
- **Controles separados**: Footer con elementos completamente independientes
- **Diseño elegante**: Bordes redondeados y sombras sutiles

### **🔍 Filtros Optimizados**
- **Diseño compacto**: Filtros al lado del contador para ahorrar espacio
- **Texto centrado**: "Mostrando X de Y cuentos" perfectamente centrado
- **Interfaz en español**: Localización completa de la interfaz
- **Feedback visual**: Badges que muestran filtros activos

### **📱 Responsive Design**
- **Mobile-first**: Diseño optimizado para móviles
- **Adaptación fluida**: Funciona perfectamente en todos los dispositivos
- **Espaciado inteligente**: Padding y márgenes adaptativos
- **Controles táctiles**: Botones del tamaño apropiado para touch

### **⚡ Rendimiento**
- **Skeletons adaptativos**: Carga visual mejorada por tema
- **Animaciones optimizadas**: Uso de GPU para transiciones suaves
- **Lazy loading**: Carga eficiente de componentes
- **Bundle optimizado**: Código minificado y tree-shaking

## 🤝 Contribución

Las contribuciones son bienvenidas. Por favor, sigue estos pasos:

1. Haz fork del proyecto
2. Crea una rama para tu característica (`git checkout -b feature/amazing-feature`)
3. Haz commit de tus cambios (`git commit -m 'Add some amazing feature'`)
4. Haz push a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

## � Scripts Disponibles

```bash
# Desarrollo
npm run dev          # Inicia servidor de desarrollo
npm run build        # Construye para producción
npm run preview      # Vista previa de la build

# Testing
npm run test.unit    # Pruebas unitarias
npm run test.e2e     # Pruebas end-to-end

# Linting y Formateo
npm run lint         # Ejecuta ESLint
npm run format       # Formatea código con Prettier

# Capacitor
npx cap sync         # Sincroniza cambios
npx cap run android  # Ejecuta en Android
npx cap run ios      # Ejecuta en iOS
```

## 📊 Características Técnicas

- **⚡ Rendimiento**: Optimizado con Vite y lazy loading
- **🔒 Seguridad**: TypeScript strict mode y validación de tipos
- **🎨 Diseño**: Sistema de design tokens con CSS variables
- **📱 Nativo**: Capacitor para funcionalidades nativas
- **🌐 PWA Ready**: Preparado para Progressive Web App
- **🔄 Estado**: Manejo eficiente del estado con React hooks
- **📦 Bundle**: Optimización automática y tree-shaking

## �📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.

## 👨‍� Desarrollado con ❤️

**StoryTale Application** - Una experiencia de lectura digital moderna y elegante.

Proyecto desarrollado con las mejores prácticas de desarrollo frontend, enfocado en la experiencia de usuario y el rendimiento.
