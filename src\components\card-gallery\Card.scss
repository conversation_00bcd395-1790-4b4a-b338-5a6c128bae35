.story-card {
  border-radius: 16px;
  position: relative;
  width: 100%;
  box-shadow: 0 4px 12px var(--shadow-color);
  background: var(--card-background);
  color: var(--text-dark);
  cursor: pointer;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px var(--shadow-color);

    .card-action {
      background-color: var(--primary-color-dark);
      color: var(--text-on-primary);
    }

    .avatar-circle {
      transform: scale(1.05);
      box-shadow: 0 6px 15px var(--shadow-color);
    }
  }

  &.deleting {
    opacity: 0.6;
    pointer-events: none;

    .card-action {
      background-color: var(--text-medium);
      color: var(--text-light);
    }
  }
}

.card-avatar {
  display: flex;
  align-items: center;
  margin-right: 10px;
  flex-shrink: 0;

  .avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-on-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    font-weight: 600;
    box-shadow: 0 4px 10px var(--shadow-color);
    border: 2px solid var(--card-background);
    transition: all 0.3s ease;
  }
}

ion-card-header {
  padding: 15px 12px 8px;
  flex-shrink: 0;

  .header-content {
    display: flex;
    align-items: flex-start;
  }
}

.card-title {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: var(--text-dark);
  font-weight: 600;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  min-height: 2.4rem;
  max-height: 2.8rem;
  flex-grow: 1;
}

.card-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 5px;

  .language-chip {
    height: 22px;
    font-size: 0.75rem;
    --background: var(--primary-color-tint);
    --color: var(--primary-color);
    margin: 0;
    padding: 0 8px;

    ion-icon {
      margin-right: 3px;
      font-size: 12px;
    }
  }

  .difficulty-badge {
    font-size: 0.65rem;
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
  }
}

.card-content {
  padding: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-details {
  padding: 0 12px 8px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-grow: 1;

  .detail-item {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-medium);

    ion-icon {
      margin-right: 6px;
      font-size: 14px;
      flex-shrink: 0;
      color: var(--primary-color);
    }

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  font-weight: 500;
  font-size: 0.8rem;
  border-top: 1px solid var(--border-color);
  transition: all 0.2s ease;
  margin-top: auto; /* Empuja la acción al fondo de la tarjeta */

  ion-icon {
    font-size: 14px;
  }

  .deleting-text {
    color: var(--text-medium);
    font-style: italic;
  }
}

/* Media queries para diferentes tamaños de pantalla */
@media (max-width: 768px) {
  .story-card {
    min-height: 200px;
  }

  .card-avatar .avatar-circle {
    width: 36px;
    height: 36px;
    font-size: 1.2rem;
  }

  .card-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .story-card {
    min-height: 180px;
  }

  .card-avatar {
    margin-right: 8px;
  }

  .card-avatar .avatar-circle {
    width: 32px;
    height: 32px;
    font-size: 1.1rem;
    border-width: 2px;
  }

  .card-title {
    font-size: 0.95rem;
    min-height: 2.2rem;
    max-height: 2.6rem;
    margin-bottom: 8px;
  }

  ion-card-header {
    padding: 12px 10px 6px;
  }

  .card-details {
    padding: 0 10px 6px;
  }

  .card-action {
    padding: 8px 10px;
  }

  .card-meta {
    gap: 6px;
  }

  .language-chip {
    height: 22px !important;
    font-size: 0.75rem !important;

    ion-icon {
      font-size: 12px !important;
    }
  }

  .difficulty-badge {
    font-size: 0.7rem !important;
    padding: 3px 6px !important;
  }
}

/* Para pantallas muy pequeñas */
@media (max-width: 350px) {
  .card-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .story-card {
    min-height: 170px;
  }

  .card-avatar .avatar-circle {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
    border-width: 2px;
  }

  .header-content {
    align-items: center;
  }
}

.button-delete {
  color: var(--text-medium);
  background-color: transparent;
  font-size: 1.2rem;
  margin-left: 10px;
  cursor: pointer;
  transition: color 0.2s;
  z-index: 2;
  border: none;
  padding: 4px;
  border-radius: 50%;

  &:hover {
    color: var(--text-dark);
    background-color: var(--primary-color-tint);
  }
}
.icon-delete .action-sheet-delete,
.action-sheet-delete .action-sheet-icon {
  color: #e74c3c !important;
}

// Personalización del ActionSheet
ion-action-sheet {
  --background: var(--surface-color) !important;
  --button-color: var(--text-dark) !important;
  --button-background: var(--surface-color) !important;
  --button-background-hover: var(--background-secondary) !important;
  --button-destructive-color: var(--danger-color) !important;
  --button-destructive-background: var(--surface-color) !important;
  --button-border-radius: 12px !important;
  --header-color: var(--text-dark) !important;
  --color: var(--text-dark) !important;
  
  .action-sheet-wrapper {
    background: var(--surface-color) !important;
    opacity: 1 !important;
  }
  
  .action-sheet-container {
    background: var(--surface-color) !important;
    opacity: 1 !important;
  }
  
  .action-sheet-group {
    background: var(--surface-color) !important;
    
    .action-sheet-button {
      background: var(--surface-color) !important;
      color: var(--text-dark) !important;
      opacity: 1 !important;
      
      &.action-sheet-destructive {
        color: var(--danger-color) !important;
      }
      
      &:hover {
        background: var(--background-secondary) !important;
      }
    }
  }
  
  .action-sheet-title {
    background: var(--surface-color) !important;
    color: var(--text-dark) !important;
    opacity: 1 !important;
  }
}

// Estilos específicos para el botón de eliminar
.action-sheet-delete {
  color: var(--danger-color) !important;
}

// Estilos adicionales para garantizar opacidad del ActionSheet
ion-action-sheet::part(backdrop) {
  background: rgba(0, 0, 0, 0.4) !important;
  opacity: 1 !important;
}

// Prevenir transparencia en modo oscuro también
@media (prefers-color-scheme: dark) {
  ion-action-sheet {
    --background: var(--surface-color) !important;
    
    .action-sheet-wrapper,
    .action-sheet-container,
    .action-sheet-group,
    .action-sheet-title {
      background: var(--surface-color) !important;
      opacity: 1 !important;
    }
  }
}