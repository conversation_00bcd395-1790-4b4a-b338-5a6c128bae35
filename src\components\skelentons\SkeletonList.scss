.skeleton-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  padding: 1rem 0.5rem;

  @media (min-width: 600px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media (min-width: 900px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.skeleton-card {
  background: var(--background-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-color);
  border: 1px solid var(--border-color-light);
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 180px;
  transition: all 0.3s ease;
}

/* Skeleton text para light theme */
ion-skeleton-text {
  --border-radius: 8px;
  --background: #e0e0e0;
  --background-rgb: 224, 224, 224;
}

/* Skeleton text para dark theme */
body.dark-theme ion-skeleton-text {
  --background: #3a3a3a;
  --background-rgb: 58, 58, 58;
}

/* Card específica para dark theme */
body.dark-theme .skeleton-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.skeleton-card-img {
  width: 100%;
  margin-bottom: 0.75rem;
}

.skeleton-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-card-title {
  margin-bottom: 0.25rem;
}

.skeleton-card-desc {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}