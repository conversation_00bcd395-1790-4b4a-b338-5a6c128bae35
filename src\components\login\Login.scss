/* ===== LOGIN MODERNO Y RESPONSIVO ===== */

/* Sobrescribir estilos globales de ion-content específicamente para Login */
.login-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
  height: 100vh;
  min-height: 100vh;
  max-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 20px;

  /* Elementos decorativos modernos */
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
}

/* Elementos decorativos adicionales */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  pointer-events: none;
  z-index: 0;

  &.decoration-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -5%;
    animation: float 6s ease-in-out infinite;
  }

  &.decoration-2 {
    width: 150px;
    height: 150px;
    bottom: 15%;
    right: -10%;
    animation: float 8s ease-in-out infinite reverse;
  }

  &.decoration-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    left: 85%;
    animation: float 7s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* Sobrescribir ion-content específicamente para el componente Login */
ion-content.login-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* Regla más específica para sobrescribir completamente los estilos globales */
ion-page ion-content.login-page {
  --background: #4a6fa5 !important;
  --color: #ffffff !important;
  background: #4a6fa5 !important;
  color: #ffffff !important;
}

/* ===== CONTENEDOR PRINCIPAL ===== */
.login-container {
  width: 100%;
  max-width: 400px;
  padding: 0;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  max-height: 100vh;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  gap: 30px;
}

/* ===== LOGO Y HEADER ===== */
.login-logo {
  text-align: center;
  flex-shrink: 0;
  z-index: 2;
  position: relative;

  img, .fallback-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
  }

  .fallback-logo {
    display: flex;
    align-items: center;
    justify-content: center;

    ion-icon {
      font-size: 40px;
      color: #ffffff;
    }
  }

  h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  p {
    font-size: 1rem;
    margin: 8px 0 0 0;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    letter-spacing: 0.3px;
  }
}

/* ===== TARJETA DE LOGIN ===== */
.login-card {
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 35px 30px;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(20px);
  flex-shrink: 0;

  /* Borde superior decorativo */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #4a6fa5;
    z-index: 3;
  }

  .login-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: #2d4373;
    text-align: center;
    margin: 0 0 30px 0;
    letter-spacing: 0.3px;
  }
}

/* ===== FORMULARIO ===== */
.login-form {
  .form-item {
    --background: rgba(248, 249, 250, 0.8);
    --border-color: rgba(74, 111, 165, 0.2);
    --border-radius: 12px;
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 56px;
    margin-bottom: 20px;
    border: 1px solid rgba(74, 111, 165, 0.15);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      --border-color: rgba(74, 111, 165, 0.3);
      border-color: rgba(74, 111, 165, 0.3);
      box-shadow: 0 4px 12px rgba(74, 111, 165, 0.1);
    }

    &:focus-within {
      --border-color: #4a6fa5;
      border-color: #4a6fa5;
      box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
    }

    ion-label {
      --color: #4a6fa5;
      font-weight: 600;
      font-size: 0.9rem;
      letter-spacing: 0.2px;
      transform: translateY(-2px); /* Mover el label un poco más arriba */
    }

    ion-input {
      --color: #2d4373;
      --placeholder-color: rgba(74, 111, 165, 0.5);
      font-size: 1rem;
      --padding-top: 14px;
      --padding-bottom: 14px;
      letter-spacing: 0.3px;
      font-weight: 500;
    }

    ion-icon {
      font-size: 1.2rem;
      margin-right: 12px;
      color: #4a6fa5;
      transition: color 0.3s ease;
      align-self: center; /* Centrar el icono verticalmente */
      display: flex;
      align-items: center;
    }

    /* Icono de mostrar/ocultar contraseña */
    .password-toggle {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      color: #4a6fa5;
      font-size: 1.1rem;
      transition: color 0.3s ease;
      z-index: 10;

      &:hover {
        color: #2d4373;
      }
    }
  }

  .error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: -16px;
    margin-bottom: 16px;
    padding: 8px 16px;
    font-weight: 600;
    background: rgba(220, 53, 69, 0.08);
    border-radius: 8px;
    border-left: 3px solid #dc3545;
    letter-spacing: 0.2px;
  }

  .login-button {
    --background: #4a6fa5;
    --background-activated: #2d4373;
    --background-hover: #415f91;
    --color: #ffffff;
    --border-radius: 12px;
    --box-shadow: 0 4px 16px rgba(74, 111, 165, 0.3);
    --ripple-color: rgba(255, 255, 255, 0.3);
    height: 52px;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-top: 20px;
    width: 100%;
    transition: all 0.3s ease;

    &:hover {
      --box-shadow: 0 6px 20px rgba(74, 111, 165, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    ion-spinner {
      --color: #ffffff;
    }
  }
}

/* ===== FOOTER Y OPCIONES SOCIALES ===== */
.login-footer {
  text-align: center;
  flex-shrink: 0;
  z-index: 2;

  .signup-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    letter-spacing: 0.2px;

    span {
      color: #ffffff;
      text-decoration: none;
      font-weight: 600;
      border-bottom: 1px solid rgba(255, 255, 255, 0.5);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-bottom-color: #ffffff;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
      }
    }
  }

  ion-text {
    p {
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.8);
      margin: 15px 0;
      letter-spacing: 0.2px;
    }
  }

  .social-login {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;

    .social-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      ion-icon {
        font-size: 1.4rem;
        color: #ffffff;
      }
    }
  }
}

/* ===== ANIMACIONES ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-container {
  animation: fadeIn 0.8s ease-out;
}

.login-card {
  animation: fadeIn 1s ease-out 0.2s both;
}

.login-footer {
  animation: fadeIn 1.2s ease-out 0.4s both;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Pantallas grandes (desktop) */
@media (min-height: 900px) and (min-width: 768px) {
  .login-container {
    gap: 40px;
    max-width: 450px;
  }

  .login-logo {
    img, .fallback-logo {
      width: 100px;
      height: 100px;
      padding: 18px;
    }

    .fallback-logo ion-icon {
      font-size: 50px;
    }

    h1 {
      font-size: 2rem;
    }

    p {
      font-size: 1.1rem;
    }
  }

  .login-card {
    padding: 45px 40px;

    .login-title {
      font-size: 1.8rem;
      margin-bottom: 35px;
    }
  }

  .login-form {
    .form-item {
      --min-height: 60px;
      margin-bottom: 24px;

      ion-input {
        font-size: 1.05rem;
        --padding-top: 16px;
        --padding-bottom: 16px;
      }

      ion-label {
        font-size: 0.95rem;
        transform: translateY(-2px);
      }

      ion-icon {
        align-self: center;
        display: flex;
        align-items: center;
      }
    }

    .login-button {
      height: 56px;
      font-size: 1.05rem;
      margin-top: 24px;
    }
  }

  .login-footer {
    .signup-text {
      font-size: 1rem;
      margin-bottom: 24px;
    }

    .social-login {
      gap: 20px;
      margin-top: 24px;

      .social-button {
        width: 52px;
        height: 52px;

        ion-icon {
          font-size: 1.5rem;
        }
      }
    }
  }
}

/* Tablets */
@media (max-width: 768px) and (min-width: 481px) {
  .login-page {
    padding: 30px 20px;
  }

  .login-container {
    max-width: 380px;
    gap: 25px;
  }

  .login-logo {
    img, .fallback-logo {
      width: 75px;
      height: 75px;
      padding: 12px;
    }

    .fallback-logo ion-icon {
      font-size: 38px;
    }

    h1 {
      font-size: 1.7rem;
    }

    p {
      font-size: 0.95rem;
    }
  }

  .login-card {
    padding: 30px 25px;

    .login-title {
      font-size: 1.5rem;
      margin-bottom: 25px;
    }
  }

  .login-form {
    .form-item {
      --min-height: 54px;
      margin-bottom: 18px;
      --padding-start: 14px;
      --padding-end: 14px;

      ion-input {
        font-size: 0.95rem;
        --padding-top: 12px;
        --padding-bottom: 12px;
      }

      ion-label {
        font-size: 0.85rem;
        transform: translateY(-2px);
      }

      ion-icon {
        font-size: 1.1rem;
        margin-right: 10px;
        align-self: center;
        display: flex;
        align-items: center;
      }
    }

    .error-message {
      font-size: 0.8rem;
      margin-top: -14px;
      margin-bottom: 14px;
      padding: 6px 14px;
    }

    .login-button {
      height: 50px;
      font-size: 0.95rem;
      margin-top: 18px;
    }
  }

  .login-footer {
    .signup-text {
      font-size: 0.85rem;
      margin-bottom: 16px;
    }

    .social-login {
      gap: 14px;
      margin-top: 18px;

      .social-button {
        width: 44px;
        height: 44px;

        ion-icon {
          font-size: 1.3rem;
        }
      }
    }
  }
}

/* Móviles */
@media (max-width: 480px) {
  .login-page {
    padding: 20px;
  }

  .login-container {
    max-width: 100%;
    gap: 20px;
    height: 100vh;
    min-height: 100vh;
    justify-content: center;
    padding: 0;
  }

  .login-logo {
    margin-bottom: 0;

    img, .fallback-logo {
      width: 70px;
      height: 70px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .fallback-logo ion-icon {
      font-size: 35px;
    }

    h1 {
      font-size: 1.6rem;
      margin-bottom: 5px;
    }

    p {
      font-size: 0.9rem;
    }
  }

  .login-card {
    padding: 25px 20px;
    border-radius: 16px;
    width: 90%;

    .login-title {
      font-size: 1.4rem;
      margin-bottom: 20px;
    }
  }

  .login-form {
    .form-item {
      --min-height: 50px;
      margin-bottom: 16px;
      --padding-start: 12px;
      --padding-end: 12px;
      --border-radius: 10px;

      ion-input {
        font-size: 0.9rem;
        --padding-top: 10px;
        --padding-bottom: 10px;
      }

      ion-label {
        font-size: 0.8rem;
        transform: translateY(3px);
      }

      ion-icon {
        font-size: 1rem;
        margin-right: 8px;
        align-self: center;
        display: flex;
        align-items: center;
      }

      .password-toggle {
        right: 12px;
        font-size: 1rem;
      }
    }

    .error-message {
      font-size: 0.75rem;
      margin-top: -12px;
      margin-bottom: 12px;
      padding: 5px 12px;
    }

    .login-button {
      height: 46px;
      font-size: 0.9rem;
      margin-top: 16px;
      --border-radius: 10px;
    }
  }

  .login-footer {
    .signup-text {
      font-size: 0.8rem;
      margin-bottom: 14px;
    }

    ion-text p {
      font-size: 0.75rem;
      margin: 12px 0;
    }

    .social-login {
      gap: 12px;
      margin-top: 16px;

      .social-button {
        width: 40px;
        height: 40px;

        ion-icon {
          font-size: 1.2rem;
        }
      }
    }
  }
}

/* Móviles muy pequeños */
@media (max-width: 360px) {
  .login-page {
    padding: 15px;
  }

  .login-container {
    gap: 15px;
    justify-content: center;
    padding: 0;
  }

  .login-logo {
    img, .fallback-logo {
      width: 60px;
      height: 60px;
      padding: 10px;
      margin-bottom: 12px;
    }

    .fallback-logo ion-icon {
      font-size: 30px;
    }

    h1 {
      font-size: 1.4rem;
    }

    p {
      font-size: 0.85rem;
    }
  }

  .login-card {
    padding: 20px 15px;
    border-radius: 14px;
    margin: 0 12px; /* Margen lateral para pantallas muy pequeñas */

    .login-title {
      font-size: 1.3rem;
      margin-bottom: 18px;
    }
  }

  .login-form {
    .form-item {
      --min-height: 46px;
      margin-bottom: 14px;
      --padding-start: 10px;
      --padding-end: 10px;

      ion-input {
        font-size: 0.85rem;
        --padding-top: 8px;
        --padding-bottom: 8px;
      }

      ion-label {
        font-size: 0.75rem;
        transform: translateY(-2px);
      }

      ion-icon {
        font-size: 0.95rem;
        margin-right: 6px;
        align-self: center;
        display: flex;
        align-items: center;
      }

      .password-toggle {
        right: 10px;
        font-size: 0.95rem;
      }
    }

    .error-message {
      font-size: 0.7rem;
      margin-top: -10px;
      margin-bottom: 10px;
      padding: 4px 10px;
    }

    .login-button {
      height: 42px;
      font-size: 0.85rem;
      margin-top: 14px;
    }
  }

  .login-footer {
    .signup-text {
      font-size: 0.75rem;
      margin-bottom: 12px;
    }

    ion-text p {
      font-size: 0.7rem;
      margin: 10px 0;
    }

    .social-login {
      gap: 10px;
      margin-top: 14px;

      .social-button {
        width: 36px;
        height: 36px;

        ion-icon {
          font-size: 1.1rem;
        }
      }
    }
  }
}

/* Ajustes para pantallas muy altas en móviles - ya no es necesario porque ahora siempre está centrado */
@media (max-width: 480px) and (min-height: 800px) {
  .login-container {
    gap: 30px;
  }

  .login-logo {
    img, .fallback-logo {
      width: 80px;
      height: 80px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .fallback-logo ion-icon {
      font-size: 40px;
    }

    h1 {
      font-size: 1.7rem;
    }

    p {
      font-size: 0.95rem;
    }
  }

  .login-card {
    padding: 30px 25px;

    .login-title {
      font-size: 1.5rem;
      margin-bottom: 25px;
    }
  }
}