/* Contenedor principal de StorytaleCreation */
.storytale-creation-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100%;
  background: var(--background-color);
}

.creator-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  background: var(--surface-color);
  border-radius: 20px;
  border: 1px solid var(--border-color-light);
  box-shadow: 0 8px 32px var(--shadow-color-light);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.creation-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color-light);

  h1 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    color: var(--text-medium);
    font-size: 0.95rem;
    max-width: 450px;
    margin: 0 auto;
    line-height: 1.5;
  }
}

.form-section {
  background: var(--background-secondary);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: var(--primary-color-tint);
    transform: translateY(-1px);
    box-shadow: 0 8px 24px var(--shadow-color);

    &::before {
      opacity: 1;
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  h2 {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 600;
    margin: 0;
  }

  ion-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-right: 8px;
  }

  .required {
    color: var(--danger-color);
    margin-left: 6px;
    font-size: 1.1rem;
    font-weight: 600;
  }
}

.title-input {
  --color: var(--text-dark);
  --placeholder-color: var(--text-medium);
  --background: var(--input-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--border-color-light);
  transition: all 0.3s ease;
  font-size: 0.95rem;

  &:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-tint);
  }

  &.ion-touched.ion-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
  }
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.option-button {
  --background: var(--card-background);
  --color: var(--text-dark);
  --border-radius: 12px;
  --box-shadow: none;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --border-style: solid;
  --border-width: 1px;
  --border-color: var(--border-color-light);

  margin: 0;
  height: auto;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    --border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px var(--shadow-color);

    &::before {
      left: 100%;
    }
  }

  &.selected {
    --background: var(--primary-color);
    --color: var(--text-on-primary);
    --border-color: var(--primary-color);
    font-weight: 600;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--primary-color-tint);
  }
}

.submit-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color-light);
}

.submit-button {
  --background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  --color: var(--text-on-primary);
  --border-radius: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 32px;
  --padding-end: 32px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
  }

  &.enabled {
    box-shadow: 0 8px 24px var(--primary-color-tint);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 32px var(--primary-color-tint);

      &::before {
        left: 100%;
      }
    }
  }

  &.disabled {
    --background: var(--text-light);
    --color: var(--text-medium);
    box-shadow: none;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.error-message {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 6px;
  margin-left: 4px;
  display: flex;
  align-items: center;
  font-weight: 500;

  &::before {
    content: '⚠';
    margin-right: 4px;
    font-size: 0.9rem;
  }
}

/* Estilos para textos descriptivos */
ion-text p {
  color: var(--text-medium);
  font-size: 0.9rem;
  margin-top: 0;
  margin-bottom: 8px;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .creator-container {
    margin: 16px;
    padding: 16px;
    border-radius: 16px;
  }

  .creation-header {
    margin-bottom: 20px;

    h1 {
      font-size: 1.7rem;
    }

    p {
      font-size: 0.9rem;
    }
  }

  .form-section {
    padding: 16px;
    margin-bottom: 12px;
  }

  .options-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 10px;
  }

  .submit-container {
    margin-top: 24px;
  }
}

@media (max-width: 480px) {
  .creator-container {
    margin: 12px;
    padding: 12px;
  }

  .options-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .option-button {
    font-size: 0.85rem;
    --padding-top: 10px;
    --padding-bottom: 10px;
  }

  .submit-button {
    --padding-start: 24px;
    --padding-end: 24px;
    font-size: 1rem;
  }
}
