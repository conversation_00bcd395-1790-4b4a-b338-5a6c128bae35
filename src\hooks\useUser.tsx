import { useEffect, useState } from "react";
import { User } from "../domain/user";
import { useAuth } from "./useAuth";
import { useFetch } from "./useFetch";

function getLocalStorageId(firebaseId: string) {
  return `user-${firebaseId}`;
}

export const useUser = () => {
  const { firebaseUser } = useAuth();

  const [user, setUser] = useState<User | null>(() => {
    if (!firebaseUser) return null;
    const storedUser = localStorage.getItem(getLocalStorageId(firebaseUser.uid));
    return storedUser ? JSON.parse(storedUser) : null;
  });

  const { get } = useFetch();

  useEffect(() => {
    if (firebaseUser && !user) {
      get<User>(`users/${firebaseUser.uid}`).then((fetchedUser) => {
        localStorage.setItem(
          getLocalStorageId(firebaseUser.uid),
          JSON.stringify(fetchedUser)
        );
        setUser(fetchedUser);
      });
    } else if (!firebaseUser && user) {
      // Si firebaseUser es null pero user no lo es, actualizar el estado
      setUser(null);
    }
  }, [firebaseUser, user]);

  return { user };
};