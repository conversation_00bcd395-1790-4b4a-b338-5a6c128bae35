interface TranslatedPhrase {
  original: string;
  translated: string;
}

export interface Genre {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface IStorytale {
  id: number;
  userId: string;
  title: string;
  content: string;
  language: string;
  difficulty: string;
  genre?: Genre; // ✅ Cambiado de string a objeto
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  translatedPhrases: TranslatedPhrase[];
}

export interface ICreateStory {
  title: string;
  language: string;
  difficulty: string;
  genre: string;
}

export interface IGenres {
  id: number;
  name: string;
}
