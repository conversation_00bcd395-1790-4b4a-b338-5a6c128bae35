import React from "react";
import "./GalleryHeader.scss";

interface GalleryHeaderProps {
  title?: string;
  subtitle?: string;
}

const GalleryHeader: React.FC<GalleryHeaderProps> = ({
  title = "Mi Colección de Cuentos",
  subtitle = "Explora tus historias personalizadas o crea nuevas para expandir tu colección",
}) => {
  return (
    <div className="gallery-header">
      <h1>{title}</h1>
      <p>{subtitle}</p>
    </div>
  );
};

export default GalleryHeader;
