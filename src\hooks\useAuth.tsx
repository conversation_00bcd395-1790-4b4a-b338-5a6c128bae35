import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  getAuth,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut,
  User,
  sendEmailVerification
} from "firebase/auth";
import { useFirebase } from "./useFirebase";
import { useBusy } from "./useBusy";

export type FirebaseLoggedUser = User & { accessToken: string };

interface AuthProps {
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  firebaseUser: FirebaseLoggedUser | null;
  originalUser: User | null;
  loading: boolean;
}

const authContext = createContext<AuthProps | null>(null);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const { app } = useFirebase();
  const auth = useRef(getAuth(app)).current;
  const [firebaseUser, setFirebaseUser] = useState<FirebaseLoggedUser | null>(
    null
  );
  const [originalUser, setOriginalUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { setIsBusy } = useBusy();

  const login = async (email: string, password: string) => {
    setIsBusy(true);

    try {
      const userCredentials = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      await setUserCredentials(userCredentials.user);

      if (!userCredentials.user.emailVerified) {
        await sendEmailVerification(userCredentials.user);
        console.log("Correo de verificación enviado");
      }
      // AuthGuard manejará la redirección basada en emailVerified
    } catch (error) {
      console.error("Error al iniciar sesión:", error);
      throw error;
    } finally {
      setLoading(false);
      setIsBusy(false);
    }
  };

  const register = async (name: string, email: string, password: string) => {
    setIsBusy(true);
    const baseUrl = import.meta.env.VITE_API_URL;
    const userData = {
      name,
      email,
      password,
    };

    try {
      // Llamar al backend para crear el usuario
      const response = await fetch(`${baseUrl}/api/auth/signup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setIsBusy(false);
        throw new Error(errorData.message || "Error al crear la cuenta");
      }

      const result = await response.json();

      setLoading(false);
      setIsBusy(false);

      login(email, password);
      return result;
    } catch (error) {
      setLoading(false);
      console.error("Error al registrar el usuario:", error);
      setIsBusy(false);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Primero actualizamos el estado para que la UI responda inmediatamente
      setFirebaseUser(null);

      // Luego limpiamos localStorage
      clearLocalStorage();

      // Finalmente cerramos sesión en Firebase
      await signOut(auth);

      // La redirección a login ocurrirá automáticamente por el router en App.tsx
      // cuando detecte que no hay usuario autenticado
    } catch (error) {
      console.error("Error al cerrar sesión:", error);
      throw error;
    }
  };

  const clearLocalStorage = () => {
    // Eliminar datos de usuario
    if (firebaseUser) {
      localStorage.removeItem(`user-${firebaseUser.uid}`);
    }

    // Buscar y eliminar cualquier otro dato relacionado con la sesión
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith("user-") ||
          key.includes("token") ||
          key.includes("auth"))
      ) {
        keysToRemove.push(key);
      }
    }
    localStorage.removeItem("app-theme");
    keysToRemove.forEach((key) => localStorage.removeItem(key));
  };

  const setUserCredentials = async (user: User) => {
    const accessToken = await user.getIdToken();
    setOriginalUser(user);
    setFirebaseUser({ ...user, accessToken });
  };

  const refreshUser = async () => {
    if (auth.currentUser) {
      try {
        // Forzar refresh del token para obtener el estado más reciente
        await auth.currentUser.reload();
        await setUserCredentials(auth.currentUser);
        console.log("Usuario refrescado, emailVerified:", auth.currentUser.emailVerified);
      } catch (error) {
        console.error("Error al refrescar usuario:", error);
      }
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        await setUserCredentials(user);
      } else {
        setFirebaseUser(null);
        setOriginalUser(null);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, [auth]);

  return (
    <authContext.Provider
      value={{ login, register, logout, refreshUser, firebaseUser, originalUser, loading }}
    >
      {children}
    </authContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(authContext);
  if (!context) throw Error("Auth context not initialized");
  return context;
};
