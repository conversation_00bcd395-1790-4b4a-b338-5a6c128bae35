import {
  IonContent,
  IonHeader,
  IonMenu,
  IonTitle,
  IonToolbar,
  IonItem,
  IonLabel,
  IonToggle,
  IonRange,
  IonIcon,
  IonList,
} from "@ionic/react";
import "./ConfigStoryReader.scss";
import { StatusBar } from "@capacitor/status-bar";
import { Capacitor } from "@capacitor/core";
import {
  volumeHighOutline,
  speedometerOutline,
  textOutline,
  contrastOutline,
  bookOutline,
} from "ionicons/icons";

export default function ConfigStoryReader() {
  const handleMenuWillOpen = async () => {
    const platform = Capacitor.getPlatform();
    if (platform === "android" || platform === "ios") {
      await StatusBar.hide();
    } else {
      console.log("StatusBar no está disponible en este dispositivo");
    }
  };

  const handleMenuDidClose = async () => {
    const platform = Capacitor.getPlatform();
    if (platform === "android" || platform === "ios") {
      await StatusBar.show();
    } else {
      console.log("StatusBar no está disponible en este dispositivo");
    }
  };

  return (
    <IonMenu
      side="end"
      contentId="main-content"
      className="menu-custom"
      onIonWillOpen={handleMenuWillOpen}
      onIonDidClose={handleMenuDidClose}
    >
      <IonHeader>
        <IonToolbar className="header-menu">
          <IonTitle className="menu-title">Reading Settings</IonTitle>
          <p className="menu-subtitle ion-padding-horizontal">Customize your reading experience</p>
        </IonToolbar>
      </IonHeader>

      <IonContent className="menu-content">
        <div className="config-section">
          <div className="section-title">
            <IonIcon icon={volumeHighOutline} />
            Audio Settings
          </div>
          <IonList lines="none">
            <IonItem className="menu-item">
              <IonIcon icon={volumeHighOutline} slot="start" />
              <IonLabel>
                <h2>Volume</h2>
                <p>Adjust reading volume</p>
              </IonLabel>
              <IonRange
                min={0}
                max={100}
                value={80}
                color="primary"
              ></IonRange>
            </IonItem>

            <IonItem className="menu-item">
              <IonIcon icon={speedometerOutline} slot="start" />
              <IonLabel>
                <h2>Reading Speed</h2>
                <p>Adjust speech rate</p>
              </IonLabel>
              <IonRange
                min={0.5}
                max={2}
                step={0.1}
                value={1}
                color="primary"
              ></IonRange>
            </IonItem>
          </IonList>
        </div>

        <div className="config-section">
          <div className="section-title">
            <IonIcon icon={bookOutline} />
            Display Settings
          </div>
          <IonList lines="none">
            <IonItem className="menu-item">
              <IonIcon icon={textOutline} slot="start" />
              <IonLabel>
                <h2>Font Size</h2>
                <p>Adjust text size</p>
              </IonLabel>
              <IonRange
                min={0.8}
                max={1.5}
                step={0.1}
                value={1}
                color="primary"
              ></IonRange>
            </IonItem>

            <IonItem className="menu-item">
              <IonIcon icon={contrastOutline} slot="start" />
              <IonLabel>
                <h2>Auto-scroll</h2>
                <p>Scroll text while reading</p>
              </IonLabel>
              <IonToggle checked={true} color="primary" slot="end"></IonToggle>
            </IonItem>
          </IonList>
        </div>
      </IonContent>
    </IonMenu>
  );
}
