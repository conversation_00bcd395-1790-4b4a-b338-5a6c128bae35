/* Contenedor principal de StorytaleList */
.storytale-list-container {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
  min-height: 100%;
}

.container-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 20px; /* Reducido para tabs */
  max-width: 1200px;
  justify-content: center;
}

.no-stories {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  border-radius: 16px;
  margin: 20px 0;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px var(--shadow-color);

  p {
    color: var(--text-medium);
    font-size: 1.1rem;
  }
}

/* Results Info with Filter Toggle - TEXTO CENTRADO */
.results-filter-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin-bottom: 15px;
  gap: 16px;
  position: relative;

  .spacer {
    width: 44px;  /* Mismo ancho que el botón de filtro */
    height: 44px;
    flex-shrink: 0;
  }

  .results-info {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    padding: 0;
    margin: 0;

    p {
      font-size: 0.9rem;
      color: var(--text-medium);
      margin: 0;
      text-align: center;  /* Centrado */
      white-space: nowrap;  /* Evita que se rompa en líneas */
    }
  }

  .filter-toggle-btn {
    position: relative;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;  /* No se encoge */

    &:hover {
      --color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.has-filters {
      background: var(--primary-color);
      --color: white;

      .filter-icon {
        color: white;
      }
    }

    .filter-icon {
      font-size: 20px;
      color: var(--primary-color);
      transition: color 0.2s ease;
    }

    .filter-badge-enhanced {
      position: absolute;
      top: 4px;
      right: 4px;
      background: var(--ion-color-danger, #eb445a);
      color: #fff;
      font-size: 11px;
      font-weight: 700;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
      border: 2px solid var(--background-color);
      z-index: 2;
    }
  }
}

/* Filters Container */
.filters-container {
  padding: 0 20px;
  margin-bottom: 20px;
  animation: slideDown 0.3s ease-out;
  transform-origin: top;

  .filter-section {
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
  }

  .filter-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    color: var(--primary-color);

    ion-icon {
      font-size: 1.2rem;
    }

    span {
      flex: 1;
      font-size: 1rem;
    }

    .clear-filters-btn {
      --color: var(--text-medium);
      --padding-start: 8px;
      --padding-end: 8px;
      font-size: 0.8rem;
      height: 28px;

      &:hover {
        --color: var(--primary-color);
      }
    }
  }

  .filter-chips {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .filter-label {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--text-dark);
    }

    .chip-group {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      ion-chip {
        --color: var(--text-medium);
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        height: 32px;
        border: 1px solid var(--border-color);

        &:hover {
          transform: translateY(-1px);
        }

        &.active {
          border-color: var(--primary-color);
        }
      }
    }
  }
}



/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scaleY(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scaleY(1);
  }
}

@media (max-width: 768px) {
  .container-cards {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    padding: 15px 15px 100px;
  }

  .gallery-header {
    margin: 15px auto 20px;

    h1 {
      font-size: 1.5rem;
    }

    p {
      font-size: 0.9rem;
    }
  }

  .search-filter-container {
    padding: 0 15px;
    margin-bottom: 15px;
    gap: 8px;

    .filter-toggle-btn {
      height: 40px;
      min-width: 45px;
      --padding-start: 10px;
      --padding-end: 10px;

      ion-icon {
        font-size: 1.1rem;
      }

      .filter-badge {
        width: 16px;
        height: 16px;
        font-size: 0.65rem;
        top: -4px;
        right: -4px;
      }
    }
  }

  .filters-container {
    padding: 0 15px;
    margin-bottom: 15px;

    .filter-section {
      padding: 15px;
    }

    .filter-header {
      margin-bottom: 12px;

      span {
        font-size: 0.9rem;
      }

      .clear-filters-btn {
        font-size: 0.75rem;
        height: 26px;
      }
    }

    .filter-chips {
      gap: 12px;
    }

    .filter-group {
      .filter-label {
        font-size: 0.85rem;
      }

      .chip-group {
        gap: 6px;

        ion-chip {
          font-size: 0.8rem;
          height: 28px;
        }
      }
    }
  }

  .results-info {
    padding: 0 15px;
    margin-bottom: 12px;

    p {
      font-size: 0.85rem;
    }
  }
}

@media (max-width: 480px) {
  .container-cards {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
    padding: 12px 12px 100px;
  }

  .search-filter-container {
    padding: 0 12px;
    gap: 6px;

    .filter-toggle-btn {
      height: 38px;
      min-width: 42px;
      --padding-start: 8px;
      --padding-end: 8px;

      ion-icon {
        font-size: 1rem;
      }

      .filter-badge {
        width: 14px;
        height: 14px;
        font-size: 0.6rem;
        top: -3px;
        right: -3px;
      }
    }
  }

  .filters-container {
    padding: 0 12px;

    .filter-section {
      padding: 12px;
    }

    .filter-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .clear-filters-btn {
        align-self: flex-end;
      }
    }

    .filter-group {
      .chip-group {
        ion-chip {
          font-size: 0.75rem;
          height: 26px;
        }
      }
    }
  }

  .results-info {
    padding: 0 12px;
  }
}

@media (max-width: 350px) {
  .container-cards {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 10px 10px 100px;
  }

  .search-filter-container {
    padding: 0 10px;
    gap: 5px;

    .filter-toggle-btn {
      height: 36px;
      min-width: 40px;
      --padding-start: 6px;
      --padding-end: 6px;

      ion-icon {
        font-size: 0.9rem;
      }

      .filter-badge {
        width: 12px;
        height: 12px;
        font-size: 0.55rem;
        top: -2px;
        right: -2px;
      }
    }
  }

  .filters-container {
    padding: 0 10px;

    .filter-section {
      padding: 10px;
    }

    .filter-chips {
      gap: 10px;
    }

    .filter-group {
      .chip-group {
        gap: 4px;

        ion-chip {
          font-size: 0.7rem;
          height: 24px;
        }
      }
    }
  }

  .results-info {
    padding: 0 10px;
  }
}
