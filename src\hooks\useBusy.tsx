import { createContext, useContext, useState } from "react";

interface IBusy {
  isBusy: boolean;
  setIsBusy: (isBusy: boolean) => void;
  isLoadingStories: boolean;
  setIsLoadingStories: (isLoadingStories: boolean) => void;
}

interface IBusyProvider {
  children: React.ReactNode;
}

const BusyContext = createContext<IBusy>({
  isBusy: false,
  setIsBusy: () => {},
  isLoadingStories: false,
  setIsLoadingStories: () => {},
});

export const BusyProvider = ({ children }: IBusyProvider) => {
  const [isBusy, setIsBusy] = useState(false);
  const [isLoadingStories, setIsLoadingStories] = useState(false);

  return (
    <BusyContext.Provider value={{ isBusy, setIsBusy, isLoadingStories, setIsLoadingStories }}>
      {children}
    </BusyContext.Provider>
  );
};

export const useBusy = () => {
  const context = useContext(BusyContext);
  if (!context) {
    throw new Error("useBusy must be used within a BusyProvider");
  }
  return context;
};
