import {
  IonButton,
  IonContent,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonPage,
  IonSpinner,
  IonText,
} from "@ionic/react";
import { useForm } from "react-hook-form";
import { useAuth } from "../../hooks/useAuth";
import { useState } from "react";
import {
  mailOutline,
  lockClosedOutline,
  logoGoogle,
  logoApple,
  logoFacebook,
  eyeOutline,
  eyeOffOutline,
  bookOutline,
} from "ionicons/icons";
import "./Login.scss";
import logoBlanco from "../../assets/logo-blanco.png";
import { useHistory } from "react-router";
import useToast from "../../hooks/useToast";

interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const { login } = useAuth();
  const { showError } = useToast();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [logoError, setLogoError] = useState(false);
  const history = useHistory();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>();

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleLogin = () => {
    handleSubmit(
      (data: LoginFormData) => {
        setLoading(true);
        login(data.email, data.password)
          .then(() => {
            // Login exitoso - AuthGuard manejará la redirección
          })
          .catch((error) => {
            showError(
              "Error al iniciar sesión: " +
                (error.message || "Credenciales incorrectas")
            );
          })
          .finally(() => setLoading(false));
      },
      () => {
        showError("Por favor, completa todos los campos correctamente");
      }
    )();
  };

  const goToRegisterPage = () => {
    history.push("/register");
  };

  return (
    <IonPage>
      <IonContent className="login-page">
        {/* Elementos decorativos modernos */}
        <div className="bg-decoration decoration-1"></div>
        <div className="bg-decoration decoration-2"></div>
        <div className="bg-decoration decoration-3"></div>

        <div className="login-container">
          <div className="login-logo">
            {logoError ? (
              <div className="fallback-logo">
                <IonIcon icon={bookOutline} />
              </div>
            ) : (
              <img
                src={logoBlanco}
                alt="Story App Logo"
                onError={() => setLogoError(true)}
              />
            )}
            <h1>Story App</h1>
            <p>Aprende idiomas con historias</p>
          </div>

          <div className="login-card">
            <h2 className="login-title">Iniciar Sesión</h2>

            <div className="login-form">
              <IonItem className="form-item">
                <IonIcon icon={mailOutline} slot="start" color="primary" />
                <IonLabel position="floating">Email</IonLabel>
                <IonInput
                  type="email"
                  {...register("email", {
                    required: true,
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  })}
                />
              </IonItem>
              {errors.email?.type === "required" && (
                <div className="error-message">El email es requerido</div>
              )}
              {errors.email?.type === "pattern" && (
                <div className="error-message">Ingresa un email válido</div>
              )}

              <IonItem className="form-item">
                <IonIcon
                  icon={lockClosedOutline}
                  slot="start"
                  color="primary"
                />
                <IonLabel position="floating">Contraseña</IonLabel>
                <IonInput
                  type={showPassword ? "text" : "password"}
                  {...register("password", {
                    required: true,
                    minLength: 6,
                  })}
                />
                <IonIcon
                  slot="end"
                  icon={showPassword ? eyeOffOutline : eyeOutline}
                  onClick={togglePasswordVisibility}
                  style={{ cursor: "pointer", color: "#4a6fa5" }}
                />
              </IonItem>
              {errors.password?.type === "required" && (
                <div className="error-message">La contraseña es requerida</div>
              )}
              {errors.password?.type === "minLength" && (
                <div className="error-message">
                  La contraseña debe tener al menos 6 caracteres
                </div>
              )}

              <IonButton
                className="login-button"
                expand="block"
                disabled={loading}
                onClick={handleLogin}
              >
                {loading ? "Iniciando sesión..." : "Iniciar Sesión"}
                {loading && <IonSpinner name="crescent" />}
              </IonButton>
            </div>
          </div>

          <div className="login-footer">
            <div className="signup-text">
              ¿No tienes una cuenta? <span onClick={goToRegisterPage}>Regístrate</span>
            </div>

            <IonText color="medium">
              <p>O inicia sesión con</p>
            </IonText>

            <div className="social-login">
              <div className="social-button">
                <IonIcon icon={logoGoogle} />
              </div>
              <div className="social-button">
                <IonIcon icon={logoFacebook} />
              </div>
              <div className="social-button">
                <IonIcon icon={logoApple} />
              </div>
            </div>
          </div>
        </div>

      </IonContent>
    </IonPage>
  );
};

export default Login;
