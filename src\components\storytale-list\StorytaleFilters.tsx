import { IonChip, IonButton, IonIcon } from "@ionic/react";
import { filterOutline, closeCircle } from "ionicons/icons";

interface StorytaleFiltersProps {
  showFilters: boolean;
  hasActiveFilters: boolean;
  clearAllFilters: () => void;
  selectedDifficulty: string;
  setSelectedDifficulty: (val: string) => void;
  selectedLanguage: string;
  setSelectedLanguage: (val: string) => void;
  selectedGenre: string;
  setSelectedGenre: (val: string) => void;
  getUniqueLanguages: () => string[];
  getUniqueGenres: () => any[];
}

const StorytaleFilters = ({
  showFilters,
  hasActiveFilters,
  clearAllFilters,
  selectedDifficulty,
  setSelectedDifficulty,
  selectedLanguage,
  setSelectedLanguage,
  selectedGenre,
  setSelectedGenre,
  getUniqueLanguages,
  getUniqueGenres,
}: StorytaleFiltersProps) => {
  if (!showFilters) return null;

  return (
    <div className="filters-container">
      <div className="filter-section">
        <div className="filter-header">
          <IonIcon icon={filterOutline} />
          <span>Filtros</span>
          {hasActiveFilters && (
            <IonButton
              fill="clear"
              size="small"
              onClick={clearAllFilters}
              className="clear-filters-btn"
            >
              <IonIcon icon={closeCircle} slot="start" />
              Limpiar Todo
            </IonButton>
          )}
        </div>
        <div className="filter-chips">
          {/* Filtro de Dificultad */}
          <div className="filter-group">
            <span className="filter-label">Dificultad:</span>
            <div className="chip-group">
              <IonChip
                className={selectedDifficulty === "todos" ? "active" : ""}
                onClick={() => setSelectedDifficulty("todos")}
              >
                Todos
              </IonChip>
              <IonChip
                className={selectedDifficulty === "easy" ? "active" : ""}
                onClick={() => setSelectedDifficulty("easy")}
              >
                Fácil
              </IonChip>
              <IonChip
                className={selectedDifficulty === "medium" ? "active" : ""}
                onClick={() => setSelectedDifficulty("medium")}
              >
                Medio
              </IonChip>
              <IonChip
                className={selectedDifficulty === "hard" ? "active" : ""}
                onClick={() => setSelectedDifficulty("hard")}
              >
                Difícil
              </IonChip>
            </div>
          </div>
          {/* Filtro de Idioma */}
          {getUniqueLanguages().length > 1 && (
            <div className="filter-group">
              <span className="filter-label">Idioma:</span>
              <div className="chip-group">
                <IonChip
                  className={selectedLanguage === "todos" ? "active" : ""}
                  onClick={() => setSelectedLanguage("todos")}
                >
                  Todos
                </IonChip>
                {getUniqueLanguages().map((language) => (
                  <IonChip
                    key={language}
                    className={
                      selectedLanguage === language.toLowerCase()
                        ? "active"
                        : ""
                    }
                    onClick={() => setSelectedLanguage(language.toLowerCase())}
                  >
                    {language}
                  </IonChip>
                ))}
              </div>
            </div>
          )}
          {/* Filtro de Género */}
          {getUniqueGenres().length > 1 && (
            <div className="filter-group">
              <span className="filter-label">Género:</span>
              <div className="chip-group">
                <IonChip
                  className={selectedGenre === "todos" ? "active" : ""}
                  onClick={() => setSelectedGenre("todos")}
                >
                  Todos
                </IonChip>
                {getUniqueGenres().map((genre) => (
                  <IonChip
                    key={genre?.id}
                    className={
                      selectedGenre === genre?.name.toLowerCase()
                        ? "active"
                        : ""
                    }
                    onClick={() =>
                      setSelectedGenre(genre?.name.toLowerCase() || "")
                    }
                  >
                    {genre?.name}
                  </IonChip>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StorytaleFilters;
