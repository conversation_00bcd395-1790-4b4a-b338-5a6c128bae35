body {
  background: var(--background-color);
  color: var(--text-dark);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Asegurar que IonApp use nuestras variables */
ion-app {
  --ion-background-color: var(--background-color);
  --ion-text-color: var(--text-dark);
  background: var(--background-color);
  color: var(--text-dark);
}

/* Estilos globales para componentes comunes */
ion-content {
  --background: var(--background-color);
  --color: var(--text-dark);
}

ion-card {
  --background: var(--card-background);
  --color: var(--text-dark);
  box-shadow: 0 4px 12px var(--shadow-color);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  -webkit-margin-start: 0 !important;
  margin-inline-start: 0 !important;
  -webkit-margin-end: 0 !important;
  margin-inline-end: 0 !important;
}

ion-item {
  --background: transparent;
  --border-color: var(--border-color);
}

ion-button {
  --box-shadow: none;
}

/* Estilos globales para headers y toolbars */
ion-header {
  --background: var(--header-background) !important;
  --color: var(--text-dark) !important;
  --border-color: var(--border-color-light) !important;
  background: var(--header-background) !important;
  color: var(--text-dark) !important;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.05);
  border-bottom: none;
  padding-top: 30px;
}

ion-toolbar {
  --background: var(--header-background) !important;
  --color: var(--text-dark) !important;
  --border-color: var(--border-color) !important;
  background: var(--header-background) !important;
  color: var(--text-dark) !important;
  --min-height: 56px;
}

ion-title {
  --color: var(--text-dark);
  color: var(--text-dark);
  font-weight: 600;
  font-size: 1.2rem;
}

/* Estilos para tabs globales */
ion-tab-bar {
  --background: var(--tab-bar-background);
  --color: var(--text-medium);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -1px 3px var(--shadow-color-light);
}

ion-tab-button {
  --color: var(--text-medium);
  --color-selected: var(--primary-color);
  --background: transparent;
  --ripple-color: var(--primary-color);
}

ion-tab-button.tab-selected {
  --color: var(--primary-color);
  --background: rgba(100, 181, 246, 0.1);
}

ion-spinner {
  --color: var(--primary-color);
}

.ion-padding {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
}
