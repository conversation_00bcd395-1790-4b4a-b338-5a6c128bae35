import { FirebaseLoggedUser, useAuth } from "./useAuth";

const getCommonHeaders = (firebaseUser: FirebaseLoggedUser | null) => {
  return {
    "Content-Type": "application/json",
    ...(firebaseUser
      ? { Authorization: `Bearer ${firebaseUser.accessToken}` }
      : {}),
  };
};

export const useFetch = () => {
  const path = "/api";
  const baseUrl = import.meta.env.VITE_API_URL;
  const { firebaseUser } = useAuth();

  const get = async <T,>(url: string): Promise<T> => {
    return fetch(`${baseUrl}${path}/${url}`, {
      headers: getCommonHeaders(firebaseUser),
    })
      .then((r) => r.json())
      .then((response) => {
        return response;
      })
      .then((response) => response as T);
  };

  const post = async <T, U>(url: string, body: U): Promise<T> => {
    return fetch(`${baseUrl}${path}/${url}`, {
      method: "POST",
      headers: getCommonHeaders(firebaseUser),
      body: JSON.stringify(body),
    })
      .then((r) => r.json())
      .then((response) => {
        return response;
      })
      .then((response) => response as T);
  };

  const del = async <T,>(url: string): Promise<T> => {
    return fetch(`${baseUrl}${path}/${url}`, {
      method: "DELETE",
      headers: getCommonHeaders(firebaseUser),
    }).then(async (r) => {
      if (r.status === 204 || r.headers.get("content-length") === "0") {
        return undefined as T;
      }
      const text = await r.text();
      if (!text) return undefined as T;
      return JSON.parse(text);
    });
  };

  return {
    get,
    post,
    del,
  };
};
