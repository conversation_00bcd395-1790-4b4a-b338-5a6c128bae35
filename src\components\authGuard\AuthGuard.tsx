import { useEffect, ReactNode, useRef } from "react";
import { useAuth } from "../../hooks/useAuth";
import { useHistory } from "react-router";
import { App } from "@capacitor/app";

interface AuthGuardProps {
  children: ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { firebaseUser, loading, refreshUser } = useAuth();
  const history = useHistory();
  const hasRedirectedRef = useRef(false);

  // Verificar email cuando la app vuelve del background
  useEffect(() => {
    const handleAppStateChange = async () => {
      console.log("App volvió del background, verificando estado del email...");
      if (firebaseUser && !firebaseUser.emailVerified) {
        await refreshUser();
      }
    };

    let appStateListener: any = null;

    // Listener para cuando la app vuelve del background (móvil)
    const setupAppListener = async () => {
      try {
        appStateListener = await App.addListener('appStateChange', ({ isActive }) => {
          if (isActive) {
            handleAppStateChange();
          }
        });
      } catch (error) {
        console.log("Capacitor App plugin no disponible, usando solo web listeners");
      }
    };

    setupAppListener();

    // Listener para cuando la app vuelve del background (web)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        handleAppStateChange();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (appStateListener) {
        appStateListener.remove();
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [firebaseUser, refreshUser]);

  useEffect(() => {
    // Solo ejecutar la lógica cuando no esté cargando y haya un usuario autenticado
    if (!loading && firebaseUser) {
      if (!firebaseUser.emailVerified) {
        // Si el email no está verificado, redirigir a la pantalla de verificación
        if (!hasRedirectedRef.current) {
          hasRedirectedRef.current = true;
          history.replace("/email-verification", { email: firebaseUser.email });
        }
      } else {
        // Si el email está verificado, resetear el flag y permitir navegación normal
        hasRedirectedRef.current = false;
        // Si estamos en la página de verificación y el email ya está verificado, redirigir a home
        if (history.location.pathname === "/email-verification") {
          history.replace("/tabs/home");
        }
      }
    }
  }, [firebaseUser, loading, history]);

  // Renderizar los children normalmente
  // El useEffect manejará las redirecciones cuando sea necesario
  return <>{children}</>;
};

export default AuthGuard;
