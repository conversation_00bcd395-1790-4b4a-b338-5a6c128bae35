name: Deploy to Google Play

env:
  APPLICATIONS: "TestApp1 CustomTimers TurtleWow"

on:
  push:
    branches:
      - main

jobs:

  build:
    runs-on: ubuntu-latest
    steps:

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install dependencies
        run: npm install

      - name: Build the app
        run: npm run build

      - name: Setup Java 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Build Android
        run: npm run build:android

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: android-build
          path: android/app/build/outputs/bundle/release

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: android-build-debug
          path: android/app/build/outputs/apk

      - name: Upload Config artifact
        uses: actions/upload-artifact@v4
        with:
          name: capacitor.config.json
          path: android/app/src/main/assets/capacitor.config.json

      - name: Upload AndroidManifest artifact
        uses: actions/upload-artifact@v4
        with:
          name: AndroidManifest.xml
          path: android/app/src/main/AndroidManifest.xml
