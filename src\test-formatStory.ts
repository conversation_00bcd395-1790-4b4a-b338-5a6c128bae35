// Ejemplo de cómo probar la función formatStory

// Datos de ejemplo en el formato que proporcionaste
const testStoryContent = JSON.stringify({
  "English": [
    ["<PERSON><PERSON> was a hedgehog."],
    ["He lived in a dark forest."],
    ["One day, he found a mysterious cave."],
    ["He decided to explore it."],
    ["Inside, he saw strange symbols."]
  ],
  "Spanish": [
    ["Eri era un erizo."],
    ["Vivía en un bosque oscuro."],
    ["Un día, encontró una cueva misteriosa."],
    ["Decidió explorarla."],
    ["Den<PERSON>, vio símbolos extraños."]
  ]
});

// Datos de ejemplo en formato de texto normal
const normalTextContent = `<PERSON><PERSON> was a hedgehog.

He lived in a dark forest.

One day, he found a mysterious cave.

He decided to explore it.

Inside, he saw strange symbols.`;

console.log('=== TESTING formatStory FUNCTION ===');
console.log('');

console.log('📋 Test 1: JSON format data');
console.log('Input:', testStoryContent.substring(0, 100) + '...');
console.log('');

console.log('📋 Test 2: Normal text format');
console.log('Input:', normalTextContent.substring(0, 100) + '...');
console.log('');

console.log('🎯 Expected behavior:');
console.log('- JSON format should extract English sentences');
console.log('- Normal text should split by double newlines');
console.log('- Both should return array of strings for iteration');
console.log('');

export { testStoryContent, normalTextContent };
