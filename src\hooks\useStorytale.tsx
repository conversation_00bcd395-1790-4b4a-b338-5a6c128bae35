import { useState } from "react";
import { useFetch } from "./useFetch";
import {
  ICreateStory,
  IGenres,
  IStorytale,
} from "../common/models/storytale.interface";
import { useUser } from "./useUser";
import { useBusy } from "./useBusy";
import useToast from "./useToast";

export const useStorytale = () => {
  const { get, post, del } = useFetch();
  const [stories, setStories] = useState<IStorytale[]>([]);
  const [genres, setGenres] = useState<IGenres[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const { setIsBusy, setIsLoadingStories } = useBusy();
  const { showSuccess, showError } = useToast();

  const createStory = async (storyData: ICreateStory) => {
    setIsBusy(true);
    setError(null);
    try {
      await post("storytale-creator", storyData);
      showSuccess("¡Cuento creado con éxito!");
    } catch (err) {
      setError("Error in story creation.");
      showError("Error al crear el cuento.");
    } finally {
      setIsBusy(false);
    }
  };

  const getStoriesByUserId = async () => {
    setError(null);
    setIsLoadingStories(true);
    try {
      const fetchedStories = (await get(
        `storytale/storytales/user/${user?.id}`
      )) as IStorytale[];
      setStories(fetchedStories);
    } catch (err) {
      setError("Error al cargar los cuentos.");
    } finally {
      setIsBusy(false);
      setIsLoadingStories(false);
    }
  };

  const getGenres = async () => {
    setError(null);
    try {
      const fetchedStories = (await get("storytale/genres")) as IGenres[];
      setGenres(fetchedStories);
    } catch (err) {
      setError("Error al cargar los cuentos.");
    } finally {
      setIsBusy(false);
    }
  };

  const deleteStorytale = async (id: number) => {
    setError(null);
    try {
      await del(`storytale/${id}`);
      setStories(stories.filter((story) => story.id !== id));
      showSuccess("¡Cuento eliminado con éxito!");
    } catch (err) {
      console.error("Error al eliminar el cuento:", err);
      setError("Error al eliminar el cuento.");
      showError("Error al eliminar el cuento.");
    } finally {
    }
  };

  return {
    stories,
    error,
    createStory,
    getStoriesByUserId,
    genres,
    getGenres,
    deleteStorytale,
  };
};
