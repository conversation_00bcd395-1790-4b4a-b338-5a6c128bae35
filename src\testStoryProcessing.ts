// Test práctico del procesamiento de datos
// Ejecuta este archivo para ver cómo se procesan tus datos exactos

import { processStoryData, currentStoryData } from './utils/storyDataProcessor';

console.log('🔄 Procesando datos de historia...\n');

// Datos de entrada (tal como los proporcionaste)
console.log('📥 DATOS DE ENTRADA:');
console.log('English sentences:', currentStoryData.English.length);
console.log('Spanish sentences:', currentStoryData.Spanish.length);
console.log('Primera frase EN:', currentStoryData.English[0][0]);
console.log('Primera frase ES:', currentStoryData.Spanish[0][0]);
console.log('---');

// Procesar los datos
const processedStory = processStoryData(currentStoryData);

console.log('📤 DATOS PROCESADOS:');
console.log('Título:', processedStory.title);
console.log('Idioma:', processedStory.language);
console.log('Dificultad:', processedStory.difficulty);
console.log('Total frases traducidas:', processedStory.translatedPhrases.length);
console.log('---');

console.log('📝 CONTENIDO (primeras 200 chars):');
console.log(processedStory.content.substring(0, 200) + '...');
console.log('---');

console.log('🔗 PRIMERAS 3 TRADUCCIONES:');
for (let i = 0; i < 3; i++) {
  const phrase = processedStory.translatedPhrases[i];
  console.log(`${i + 1}. "${phrase.original}" → "${phrase.translated}"`);
}
console.log('---');

console.log('✅ ¡Datos procesados correctamente!');
console.log('🎯 El StoryReader ahora puede usar estos datos con el nuevo sistema de traducción');

export { processedStory };
