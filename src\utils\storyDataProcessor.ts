// Utilidad para procesar los datos de historia con el formato específico
import { IStorytale } from '../common/models/storytale.interface';

// Interfaz para los datos de entrada tal como se están renderizando
interface StoryData {
  English: string[][];
  Spanish: string[][];
}

/**
 * Procesa los datos de historia desde el formato actual a IStorytale
 * @param storyData - Datos en formato {"English": [["frase"]], "Spanish": [["frase"]]}
 * @param title - Título del cuento
 * @param difficulty - Dificultad del cuento
 * @returns IStorytale formateado para el StoryReader
 */
export const processStoryData = (
  storyData: StoryData,
  title: string = "<PERSON>ri the Hedgehog",
  difficulty: string = "Beginner"
): IStorytale => {
  // Extraer las frases de los arrays anidados
  const englishSentences = storyData.English.map(sentenceArray => sentenceArray[0]);
  const spanishSentences = storyData.Spanish.map(sentenceArray => sentenceArray[0]);

  // Crear el contenido del cuento (texto completo en inglés)
  const content = englishSentences.join('\n\n');

  // Crear las frases traducidas emparejando inglés con español
  const translatedPhrases = englishSentences.map((englishSentence, index) => ({
    original: englishSentence,
    translated: spanishSentences[index] || englishSentence // Fallback al inglés si no hay traducción
  }));

  // Retornar el objeto IStorytale completo
  return {
    id: 1,
    userId: "demo-user",
    title: title,
    content: content,
    language: "English",
    difficulty: difficulty,
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    translatedPhrases: translatedPhrases
  };
};

/**
 * Datos de ejemplo tal como se están renderizando actualmente
 */
export const currentStoryData: StoryData = {
  "English": [
    ["Eri was a hedgehog."],
    ["He lived in a dark forest."],
    ["One day, he found a mysterious cave."],
    ["He decided to explore it."],
    ["Inside, he saw strange symbols."],
    ["He felt a chill."],
    ["Suddenly, he heard a noise."],
    ["He turned around."],
    ["A shadowy figure appeared."],
    ["Eri's heart raced."],
    ["The figure spoke."],
    ["'You have trespassed.'"],
    ["Eri trembled."],
    ["'Who are you?'"],
    ["The figure laughed."],
    ["'I am the guardian.'"],
    ["Eri was terrified."],
    ["'What do you want?'"],
    ["The guardian pointed."],
    ["'The crystal.'"],
    ["Eri saw a glowing crystal."],
    ["He hesitated."],
    ["'Take it.'"],
    ["Eri grabbed the crystal."],
    ["The guardian vanished."],
    ["Eri felt a surge of power."],
    ["He left the cave."],
    ["The forest was silent."],
    ["Eri knew he had changed."]
  ],
  "Spanish": [
    ["Eri era un erizo."],
    ["Vivía en un bosque oscuro."],
    ["Un día, encontró una cueva misteriosa."],
    ["Decidió explorarla."],
    ["Dentro, vio símbolos extraños."],
    ["Sintió un escalofrío."],
    ["De repente, escuchó un ruido."],
    ["Se dio la vuelta."],
    ["Apareció una figura sombría."],
    ["El corazón de Eri latía rápido."],
    ["La figura habló."],
    ["'Has invadido.'"],
    ["Eri temblaba."],
    ["'¿Quién eres?'"],
    ["La figura se rió."],
    ["'Soy el guardián.'"],
    ["Eri estaba aterrorizado."],
    ["'¿Qué quieres?'"],
    ["El guardián señaló."],
    ["'El cristal.'"],
    ["Eri vio un cristal brillante."],
    ["Vaciló."],
    ["'Tómalo.'"],
    ["Eri agarró el cristal."],
    ["El guardián desapareció."],
    ["Eri sintió una oleada de poder."],
    ["Salió de la cueva."],
    ["El bosque estaba en silencio."],
    ["Eri sabía que había cambiado."]
  ]
};

/**
 * Historia procesada lista para usar en StoryReader
 */
export const processedStory = processStoryData(currentStoryData);

// Ejemplo de uso:
/*
import { processedStory, processStoryData } from './utils/storyDataProcessor';

// Usar la historia ya procesada
const story = processedStory;

// O procesar datos nuevos
const newStoryData = { English: [...], Spanish: [...] };
const newStory = processStoryData(newStoryData, "Nuevo Título", "Intermediate");
*/
