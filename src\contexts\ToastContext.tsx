import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { IonToast } from "@ionic/react";
import "./ToastContext.scss";

export type ToastColor = "success" | "danger" | "warning" | "primary";
export type ToastPosition = "top" | "bottom" | "middle";

interface ToastOptions {
  duration?: number;
  position?: ToastPosition;
  buttons?: boolean;
}

interface ToastContextType {
  showSuccess: (message: string, options?: ToastOptions) => void;
  showError: (message: string, options?: ToastOptions) => void;
  showWarning: (message: string, options?: ToastOptions) => void;
  showInfo: (message: string, options?: ToastOptions) => void;
  showToast: (message: string, color?: ToastColor, options?: ToastOptions) => void;
  hideToast: () => void;
  isVisible: boolean;
  message: string;
  color: ToastColor;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
  defaultOptions?: ToastOptions;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ 
  children, 
  defaultOptions = {} 
}) => {
  const [showToastState, setShowToastState] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastColor, setToastColor] = useState<ToastColor>("success");
  const [toastDuration, setToastDuration] = useState(3000);
  const [toastPosition, setToastPosition] = useState<ToastPosition>("bottom");
  const [showButtons, setShowButtons] = useState(true);

  const showToastMessage = (
    message: string, 
    color: ToastColor = "success", 
    options: ToastOptions = {}
  ) => {
    const finalOptions = { ...defaultOptions, ...options };
    
    setToastMessage(message);
    setToastColor(color);
    setToastDuration(finalOptions.duration || 3000);
    setToastPosition(finalOptions.position || "bottom");
    setShowButtons(finalOptions.buttons !== false);
    setShowToastState(true);
  };

  const showSuccess = (message: string, options?: ToastOptions) => {
    showToastMessage(message, "success", options);
  };

  const showError = (message: string, options?: ToastOptions) => {
    showToastMessage(message, "danger", options);
  };

  const showWarning = (message: string, options?: ToastOptions) => {
    showToastMessage(message, "warning", options);
  };

  const showInfo = (message: string, options?: ToastOptions) => {
    showToastMessage(message, "primary", options);
  };

  const hideToast = () => {
    setShowToastState(false);
  };

  const contextValue: ToastContextType = {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showToast: showToastMessage,
    hideToast,
    isVisible: showToastState,
    message: toastMessage,
    color: toastColor,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <IonToast
        isOpen={showToastState}
        onDidDismiss={hideToast}
        message={toastMessage}
        duration={toastDuration}
        position={toastPosition}
        color={toastColor}
        cssClass="custom-bottom-toast"
        buttons={showButtons ? [
          {
            text: "Cerrar",
            role: "cancel",
          },
        ] : undefined}
      />
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

export default ToastProvider;
