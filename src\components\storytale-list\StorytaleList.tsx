import { useEffect, useState } from "react";
import {
  IonButton,
  IonPage,
  IonContent,
  IonIcon,
} from "@ionic/react";
import { filterOutline } from "ionicons/icons";
import CardGallery from "../card-gallery/Card";
import { useStorytale } from "../../hooks/useStorytale";
import "./StorytaleList.scss";
import { useUser } from "../../hooks/useUser";
import { IStorytale } from "../../common/models/storytale.interface";
import { useHistory, useLocation } from "react-router";
import StorytaleFilters from "./StorytaleFilters";
import GalleryHeader from "./GalleryHeader";
import { useBusy } from "../../hooks/useBusy";
import SkeletonList from "../skelentons/SkeletonList";
import Header from "../header/Header";

const StorytaleList = () => {
  const { stories, getStoriesByUserId, deleteStorytale } =
    useStorytale();
  const { user } = useUser();
  const history = useHistory();
  const location = useLocation();
  const { isLoadingStories } = useBusy();

  // Filter states
  const [searchText, setSearchText] = useState("");
  const [selectedDifficulty, setSelectedDifficulty] = useState("todos");
  const [selectedLanguage, setSelectedLanguage] = useState("todos");
  const [selectedGenre, setSelectedGenre] = useState("todos");
  const [showFilters, setShowFilters] = useState(false);

  // Estado para feedback de borrado por card
  const [deletingId, setDeletingId] = useState<number | null>(null);

  const handleClick = (story: IStorytale) => {
    history.push("/story-reader", { story });
  };

  // Recarga solo si viene de creación de cuento
  useEffect(() => {
    if (
      location.state &&
      typeof (location.state as { refresh?: boolean }).refresh === "boolean" &&
      (location.state as { refresh?: boolean }).refresh
    ) {
      getStoriesByUserId();
      // Limpia el estado para evitar recargas futuras
      history.replace({ ...location, state: undefined });
    }
  }, [location, getStoriesByUserId, history]);

  useEffect(() => {
    if (user?.id) {
      getStoriesByUserId();
    }
  }, [user]);

  // Get unique values for filter options
  const getUniqueLanguages = () => {
    const languages = stories.map((story) => story.language);
    return [...new Set(languages)].sort();
  };

  const getUniqueGenres = () => {
    const genreMap = new Map();
    stories.forEach((story) => {
      if (story.genre && story.genre.id) {
        genreMap.set(story.genre.id, story.genre);
      }
    });
    return Array.from(genreMap.values());
  };

  // Filter and sort stories
  const getFilteredStories = () => {
    let filtered = stories;

    // Filter by search text
    if (searchText.trim()) {
      filtered = filtered.filter(
        (story) =>
          story.title.toLowerCase().includes(searchText.toLowerCase()) ||
          story.content.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Filtrar por dificultad
    if (selectedDifficulty !== "todos") {
      filtered = filtered.filter(
        (story) =>
          story.difficulty.toLowerCase() === selectedDifficulty.toLowerCase()
      );
    }

    // Filtrar por idioma
    if (selectedLanguage !== "todos") {
      filtered = filtered.filter(
        (story) =>
          story.language.toLowerCase() === selectedLanguage.toLowerCase()
      );
    }

    // Filtrar por género
    if (selectedGenre !== "todos") {
      filtered = filtered.filter(
        (story) =>
          story.genre?.name.toLowerCase() === selectedGenre.toLowerCase()
      );
    }

    // Sort by creation date (newest first)
    return filtered.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  };

  const filteredStories = getFilteredStories();

  // Limpiar todos los filtros
  const clearAllFilters = () => {
    setSearchText("");
    setSelectedDifficulty("todos");
    setSelectedLanguage("todos");
    setSelectedGenre("todos");
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Verificar si hay filtros activos
  const hasActiveFilters = Boolean(
    searchText.trim() ||
      selectedDifficulty !== "todos" ||
      selectedLanguage !== "todos" ||
      selectedGenre !== "todos"
  );

  return (
    <IonPage>
      <Header />
      <IonContent>
        <div className="storytale-list-container">
          {/* Gallery Header (reemplaza la cabecera anterior) */}
          <GalleryHeader />

          {/* Results Info with Filter Toggle */}
          <div className="results-filter-container">
            <div className="spacer"></div>
            <div className="results-info">
              <p>
                Mostrando {filteredStories.length} de {stories.length} cuentos
                {hasActiveFilters && " (filtrados)"}
              </p>
            </div>
            <IonButton
              fill="clear"
              className={`filter-toggle-btn ${hasActiveFilters ? "has-filters" : ""}`}
              onClick={toggleFilters}
            >
              <IonIcon icon={filterOutline} slot="icon-only" className="filter-icon" />
              {hasActiveFilters && (
                <span className="filter-badge-enhanced">
                  {
                    [
                      searchText.trim(),
                      selectedDifficulty !== "todos",
                      selectedLanguage !== "todos",
                      selectedGenre !== "todos",
                    ].filter(Boolean).length
                  }
                </span>
              )}
            </IonButton>
          </div>

          {/* Collapsible Filters */}
          <StorytaleFilters
            showFilters={showFilters}
            hasActiveFilters={hasActiveFilters}
            clearAllFilters={clearAllFilters}
            selectedDifficulty={selectedDifficulty}
            setSelectedDifficulty={setSelectedDifficulty}
            selectedLanguage={selectedLanguage}
            setSelectedLanguage={setSelectedLanguage}
            selectedGenre={selectedGenre}
            setSelectedGenre={setSelectedGenre}
            getUniqueLanguages={getUniqueLanguages}
            getUniqueGenres={getUniqueGenres}
          />

          {isLoadingStories ? (
            <SkeletonList count={12} />
          ) : (
            <div className="container-cards">
              {filteredStories.length > 0 ? (
                filteredStories.map((story: IStorytale) => (
                  <CardGallery
                    key={story.id}
                    id={story.id}
                    title={story.title}
                    laguage={story.language}
                    difficulty={story.difficulty}
                    genre={story.genre?.name || "Ficción"}
                    createdAt={story.createdAt}
                    onClick={() => handleClick(story)}
                    onDelete={async (id?: number) => {
                      if (id !== undefined) {
                        setDeletingId(id);
                        await deleteStorytale(id);
                        await getStoriesByUserId();
                        setDeletingId(null);
                      }
                    }}
                    isDeleting={deletingId === story.id}
                  />
                ))
              ) : (
                <div className="no-stories">
                  {hasActiveFilters ? (
                    <div>
                      <p>No hay cuentos que coincidan con los filtros actuales.</p>
                      <IonButton
                        fill="outline"
                        onClick={clearAllFilters}
                        className="clear-filters-btn"
                      >
                        Limpiar Filtros
                      </IonButton>
                    </div>
                  ) : (
                    <p>No hay cuentos disponibles aún. ¡Crea tu primer cuento!</p>
                  )}
                </div>
              )}
            </div>
          )}

        </div>
      </IonContent>
    </IonPage>
  );
};

export default StorytaleList;
